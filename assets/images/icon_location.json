{"v": "4.8.0", "meta": {"g": "LottieFiles AE ", "a": "", "k": "", "d": "", "tc": ""}, "fr": 25, "ip": 0, "op": 50, "w": 500, "h": 500, "nm": "03 Location", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "Main Stroke width - Color Ctrl", "parent": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "a": {"a": 0, "k": [50, 50, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Stroke width", "np": 3, "mn": "ADBE Slider Control", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Slide<PERSON>", "mn": "ADBE Slider Control-0001", "ix": 1, "v": {"a": 0, "k": 16, "ix": 1}}]}], "ip": 0, "op": 50, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Location", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.63], "y": [1]}, "o": {"x": [0.37], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.63], "y": [1]}, "o": {"x": [0.37], "y": [0]}, "t": 7, "s": [25.508]}, {"i": {"x": [0.63], "y": [1]}, "o": {"x": [0.37], "y": [0]}, "t": 15, "s": [-19.03]}, {"i": {"x": [0.63], "y": [1]}, "o": {"x": [0.37], "y": [0]}, "t": 25, "s": [9.266]}, {"i": {"x": [0.63], "y": [1]}, "o": {"x": [0.37], "y": [0]}, "t": 34, "s": [-5.44]}, {"t": 42, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [51.262, 240.686, 0], "ix": 2}, "a": {"a": 0, "k": [1.262, 190.686, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 7, "s": [{"i": [[-12.867, -21.853], [0, 0], [0, 0], [0, 25.693], [0, 0], [70.162, 0], [0, 0], [0, -70.162], [0, 0]], "o": [[0, 0], [0, 0], [13.188, -22.049], [0, 0], [0, -70.162], [0, 0], [-70.162, 0], [0, 0], [0, 25.36]], "v": [[-106.125, 9.999], [0.264, 190.686], [108.149, 10.315], [128.302, -62.639], [128.302, -62.639], [1.263, -189.679], [1.262, -189.679], [-125.777, -62.639], [-125.777, -62.112]], "c": true}]}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 15, "s": [{"i": [[-8.248, -14.008], [0, 0], [0, 0], [0, 16.469], [0, 0], [44.973, 0], [0, 0], [0, -44.973], [0, 0]], "o": [[0, 0], [0, 0], [8.454, -14.133], [0, 0], [0, -44.973], [0, 0], [-44.973, 0], [0, 0], [0, 16.255]], "v": [[-68.233, 74.502], [-0.039, 190.32], [69.114, 74.705], [82.032, 27.942], [82.032, 27.942], [0.601, -53.489], [0.6, -53.489], [-80.831, 27.942], [-80.831, 28.28]], "c": true}]}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 41, "s": [{"i": [[-8.248, -14.008], [0, 0], [0, 0], [0, 16.469], [0, 0], [44.973, 0], [0, 0], [0, -44.973], [0, 0]], "o": [[0, 0], [0, 0], [8.454, -14.133], [0, 0], [0, -44.973], [0, 0], [-44.973, 0], [0, 0], [0, 16.255]], "v": [[-68.233, 74.502], [-0.039, 190.32], [69.114, 74.705], [82.032, 27.942], [82.032, 27.942], [0.601, -53.489], [0.6, -53.489], [-80.831, 27.942], [-80.831, 28.28]], "c": true}]}, {"t": 49, "s": [{"i": [[-12.867, -21.853], [0, 0], [0, 0], [0, 25.693], [0, 0], [70.162, 0], [0, 0], [0, -70.162], [0, 0]], "o": [[0, 0], [0, 0], [13.188, -22.049], [0, 0], [0, -70.162], [0, 0], [-70.162, 0], [0, 0], [0, 25.36]], "v": [[-106.125, 9.999], [0.264, 190.686], [108.149, 10.315], [128.302, -62.639], [128.302, -62.639], [1.263, -189.679], [1.262, -189.679], [-125.777, -62.639], [-125.777, -62.112]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 7, "s": [{"i": [[0, -32.977], [32.977, 0], [0, 32.977], [-32.977, 0]], "o": [[0, 32.977], [-32.977, 0], [0, -32.977], [32.977, 0]], "v": [[59.974, -60.081], [0.264, -0.371], [-59.446, -60.081], [0.264, -119.791]], "c": true}]}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 15, "s": [{"i": [[0, -21.138], [21.138, 0], [0, 21.138], [-21.138, 0]], "o": [[0, 21.138], [-21.138, 0], [0, -21.138], [21.138, 0]], "v": [[38.234, 29.581], [-0.039, 67.855], [-38.313, 29.581], [-0.039, -8.692]], "c": true}]}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 41, "s": [{"i": [[0, -21.138], [21.138, 0], [0, 21.138], [-21.138, 0]], "o": [[0, 21.138], [-21.138, 0], [0, -21.138], [21.138, 0]], "v": [[38.234, 29.581], [-0.039, 67.855], [-38.313, 29.581], [-0.039, -8.692]], "c": true}]}, {"t": 49, "s": [{"i": [[0, -32.977], [32.977, 0], [0, 32.977], [-32.977, 0]], "o": [[0, 32.977], [-32.977, 0], [0, -32.977], [32.977, 0]], "v": [[59.974, -60.081], [0.264, -0.371], [-59.446, -60.081], [0.264, -119.791]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000000298, 0.800000011921, 0.800000011921, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 1, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 50, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Map 3", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [50, 50, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[196.085, 212.702], [-196.085, 212.702], [-195.489, -209.77], [196.681, -209.77]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 17, "s": [100]}, {"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 38, "s": [100]}, {"t": 49, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 2, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 50, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Location mask", "parent": 1, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.63], "y": [1]}, "o": {"x": [0.37], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.63], "y": [1]}, "o": {"x": [0.37], "y": [0]}, "t": 7, "s": [25.508]}, {"i": {"x": [0.63], "y": [1]}, "o": {"x": [0.37], "y": [0]}, "t": 15, "s": [-19.03]}, {"i": {"x": [0.63], "y": [1]}, "o": {"x": [0.37], "y": [0]}, "t": 25, "s": [9.266]}, {"i": {"x": [0.63], "y": [1]}, "o": {"x": [0.37], "y": [0]}, "t": 34, "s": [-5.44]}, {"t": 42, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [51.262, 240.686, 0], "ix": 2}, "a": {"a": 0, "k": [1.262, 190.686, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 7, "s": [{"i": [[-12.867, -21.853], [0, 0], [0, 0], [0, 25.693], [0, 0], [70.162, 0], [0, 0], [0, -70.162], [0, 0]], "o": [[0, 0], [0, 0], [13.188, -22.049], [0, 0], [0, -70.162], [0, 0], [-70.162, 0], [0, 0], [0, 25.36]], "v": [[-106.125, 9.999], [0.264, 190.686], [108.149, 10.315], [128.302, -62.639], [128.302, -62.639], [1.263, -189.679], [1.262, -189.679], [-125.777, -62.639], [-125.777, -62.112]], "c": true}]}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 15, "s": [{"i": [[-8.248, -14.008], [0, 0], [0, 0], [0, 16.469], [0, 0], [44.973, 0], [0, 0], [0, -44.973], [0, 0]], "o": [[0, 0], [0, 0], [8.454, -14.133], [0, 0], [0, -44.973], [0, 0], [-44.973, 0], [0, 0], [0, 16.255]], "v": [[-68.233, 74.502], [-0.039, 190.32], [69.114, 74.705], [82.032, 27.942], [82.032, 27.942], [0.601, -53.489], [0.6, -53.489], [-80.831, 27.942], [-80.831, 28.28]], "c": true}]}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 41, "s": [{"i": [[-8.248, -14.008], [0, 0], [0, 0], [0, 16.469], [0, 0], [44.973, 0], [0, 0], [0, -44.973], [0, 0]], "o": [[0, 0], [0, 0], [8.454, -14.133], [0, 0], [0, -44.973], [0, 0], [-44.973, 0], [0, 0], [0, 16.255]], "v": [[-68.233, 74.502], [-0.039, 190.32], [69.114, 74.705], [82.032, 27.942], [82.032, 27.942], [0.601, -53.489], [0.6, -53.489], [-80.831, 27.942], [-80.831, 28.28]], "c": true}]}, {"t": 49, "s": [{"i": [[-12.867, -21.853], [0, 0], [0, 0], [0, 25.693], [0, 0], [70.162, 0], [0, 0], [0, -70.162], [0, 0]], "o": [[0, 0], [0, 0], [13.188, -22.049], [0, 0], [0, -70.162], [0, 0], [-70.162, 0], [0, 0], [0, 25.36]], "v": [[-106.125, 9.999], [0.264, 190.686], [108.149, 10.315], [128.302, -62.639], [128.302, -62.639], [1.263, -189.679], [1.262, -189.679], [-125.777, -62.639], [-125.777, -62.112]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "st", "c": {"a": 0, "k": [0.20000000298, 0.800000011921, 0.800000011921, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 1, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 50, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Map", "parent": 1, "tt": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [50, 50, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[196.085, 179.571], [72.318, 78.345]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000000298, 0.800000011921, 0.800000011921, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-56.293, 134.419], [-195.097, 21.304]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000000298, 0.800000011921, 0.800000011921, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-81.992, 212.702], [-195.762, 128.696]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000000298, 0.800000011921, 0.800000011921, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-56.312, 133.27], [195.736, 24.179]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000000298, 0.800000011921, 0.800000011921, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 4, "s": [0]}, {"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 19.6, "s": [100]}, {"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 30, "s": [100]}, {"t": 43, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 2, "ix": 5, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 50, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Location mask 2", "parent": 1, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.63], "y": [1]}, "o": {"x": [0.37], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.63], "y": [1]}, "o": {"x": [0.37], "y": [0]}, "t": 7, "s": [25.508]}, {"i": {"x": [0.63], "y": [1]}, "o": {"x": [0.37], "y": [0]}, "t": 15, "s": [-19.03]}, {"i": {"x": [0.63], "y": [1]}, "o": {"x": [0.37], "y": [0]}, "t": 25, "s": [9.266]}, {"i": {"x": [0.63], "y": [1]}, "o": {"x": [0.37], "y": [0]}, "t": 34, "s": [-5.44]}, {"t": 42, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [51.262, 240.686, 0], "ix": 2}, "a": {"a": 0, "k": [1.262, 190.686, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 7, "s": [{"i": [[-12.867, -21.853], [0, 0], [0, 0], [0, 25.693], [0, 0], [70.162, 0], [0, 0], [0, -70.162], [0, 0]], "o": [[0, 0], [0, 0], [13.188, -22.049], [0, 0], [0, -70.162], [0, 0], [-70.162, 0], [0, 0], [0, 25.36]], "v": [[-106.125, 9.999], [0.264, 190.686], [108.149, 10.315], [128.302, -62.639], [128.302, -62.639], [1.263, -189.679], [1.262, -189.679], [-125.777, -62.639], [-125.777, -62.112]], "c": true}]}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 15, "s": [{"i": [[-8.248, -14.008], [0, 0], [0, 0], [0, 16.469], [0, 0], [44.973, 0], [0, 0], [0, -44.973], [0, 0]], "o": [[0, 0], [0, 0], [8.454, -14.133], [0, 0], [0, -44.973], [0, 0], [-44.973, 0], [0, 0], [0, 16.255]], "v": [[-68.233, 74.502], [-0.039, 190.32], [69.114, 74.705], [82.032, 27.942], [82.032, 27.942], [0.601, -53.489], [0.6, -53.489], [-80.831, 27.942], [-80.831, 28.28]], "c": true}]}, {"i": {"x": 0.6, "y": 1}, "o": {"x": 0.4, "y": 0}, "t": 41, "s": [{"i": [[-8.248, -14.008], [0, 0], [0, 0], [0, 16.469], [0, 0], [44.973, 0], [0, 0], [0, -44.973], [0, 0]], "o": [[0, 0], [0, 0], [8.454, -14.133], [0, 0], [0, -44.973], [0, 0], [-44.973, 0], [0, 0], [0, 16.255]], "v": [[-68.233, 74.502], [-0.039, 190.32], [69.114, 74.705], [82.032, 27.942], [82.032, 27.942], [0.601, -53.489], [0.6, -53.489], [-80.831, 27.942], [-80.831, 28.28]], "c": true}]}, {"t": 49, "s": [{"i": [[-12.867, -21.853], [0, 0], [0, 0], [0, 25.693], [0, 0], [70.162, 0], [0, 0], [0, -70.162], [0, 0]], "o": [[0, 0], [0, 0], [13.188, -22.049], [0, 0], [0, -70.162], [0, 0], [-70.162, 0], [0, 0], [0, 25.36]], "v": [[-106.125, 9.999], [0.264, 190.686], [108.149, 10.315], [128.302, -62.639], [128.302, -62.639], [1.263, -189.679], [1.262, -189.679], [-125.777, -62.639], [-125.777, -62.112]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "st", "c": {"a": 0, "k": [0.20000000298, 0.800000011921, 0.800000011921, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 1, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 50, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Map 2", "parent": 1, "tt": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 180, "ix": 10}, "p": {"a": 0, "k": [50, 56, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[195.21, 178.946], [72.318, 78.345]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000000298, 0.800000011921, 0.800000011921, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-56.324, 133.326], [-196.222, 20.179]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000000298, 0.800000011921, 0.800000011921, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-77.867, 215.827], [-196.012, 128.321]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000000298, 0.800000011921, 0.800000011921, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-56.312, 133.27], [195.736, 24.679]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.20000000298, 0.800000011921, 0.800000011921, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 4, "s": [0]}, {"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 19.6, "s": [100]}, {"i": {"x": [0.6], "y": [1]}, "o": {"x": [0.4], "y": [0]}, "t": 30, "s": [100]}, {"t": 43, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 2, "ix": 5, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 50, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 3, "nm": "Zero | troke width - Color Ctrl", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [250, 250, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 50, "st": 0, "bm": 0}], "markers": []}