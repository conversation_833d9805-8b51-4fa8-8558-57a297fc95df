<svg width="87" height="87" viewBox="0 0 87 87" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#vx6cy7swna)">
        <path fill-rule="evenodd" clip-rule="evenodd" d="M20 43.5C20 30.521 30.522 20 43.5 20 56.479 20 67 30.521 67 43.5 67 56.478 56.479 67 43.5 67 30.522 67 20 56.478 20 43.5zm20.182-12.237a3.319 3.319 0 0 1 6.637 0l-2.553 20.75a.765.765 0 1 1-1.53 0l-2.553-20.75zM43.5 54.38a2.337 2.337 0 1 1 0 4.674 2.337 2.337 0 0 1 0-4.674z" fill="#FF7369" fill-opacity=".4"/>
    </g>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M15 29.5C15 13.76 27.76 1 43.5 1S72 13.76 72 29.5 59.24 58 43.5 58 15 45.24 15 29.5zm24.477-14.841a4.025 4.025 0 0 1 8.048 0l-3.096 25.166a.928.928 0 1 1-1.856 0L39.477 14.66zM43.5 42.696a2.834 2.834 0 1 1 0 5.669 2.834 2.834 0 0 1 0-5.669z" fill="#FF7369"/>
    <defs>
        <filter id="vx6cy7swna" x="0" y="0" width="87" height="87" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feGaussianBlur stdDeviation="10" result="effect1_foregroundBlur_1236_5316"/>
        </filter>
    </defs>
</svg>
