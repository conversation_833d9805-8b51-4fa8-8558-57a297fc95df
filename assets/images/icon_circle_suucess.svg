<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="72" height="72" rx="36" fill="#24D39E"/>
<g clip-path="url(#clip0_2414_10428)">
<g filter="url(#filter0_d_2414_10428)">
<path d="M49.3334 26L31.0001 44.3333L22.6667 36" stroke="white" stroke-width="6" stroke-linecap="round" stroke-linejoin="round" shape-rendering="crispEdges"/>
</g>
</g>
<defs>
<filter id="filter0_d_2414_10428" x="11.6667" y="19" width="48.6666" height="40.334" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.020625 0 0 0 0 0.275 0 0 0 0 0.121904 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2414_10428"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2414_10428" result="shape"/>
</filter>
<clipPath id="clip0_2414_10428">
<rect width="40" height="40" fill="white" transform="translate(16 16)"/>
</clipPath>
</defs>
</svg>
