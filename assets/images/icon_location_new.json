{"nm": "03 Location", "ddd": 0, "h": 500, "w": 500, "meta": {"g": "LottieFiles AE "}, "layers": [{"ty": 3, "nm": "Main Stroke width - Color Ctrl", "sr": 1, "st": 0, "op": 50, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [50, 50, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0, "ix": 11}}, "ef": [{"ty": 5, "mn": "ADBE Slider Control", "nm": "Stroke width", "ix": 1, "en": 1, "ef": [{"ty": 0, "mn": "ADBE Slider Control-0001", "nm": "Slide<PERSON>", "ix": 1, "v": {"a": 0, "k": 16, "ix": 1}}]}], "ind": 1, "parent": 8}, {"ty": 4, "nm": "Location", "sr": 1, "st": 0, "op": 50, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [1.262, 190.686, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [51.262, 240.686, 0], "ix": 2}, "r": {"a": 1, "k": [{"o": {"x": 0.37, "y": 0}, "i": {"x": 0.63, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.37, "y": 0}, "i": {"x": 0.63, "y": 1}, "s": [25.508], "t": 7}, {"o": {"x": 0.37, "y": 0}, "i": {"x": 0.63, "y": 1}, "s": [-19.03], "t": 15}, {"o": {"x": 0.37, "y": 0}, "i": {"x": 0.63, "y": 1}, "s": [9.266], "t": 25}, {"o": {"x": 0.37, "y": 0}, "i": {"x": 0.63, "y": 1}, "s": [-5.44], "t": 34}, {"s": [0], "t": 42}], "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [{"c": true, "i": [[-12.867, -21.853], [0, 0], [0, 0], [0, 25.693], [0, 0], [70.162, 0], [0, 0], [0, -70.162], [0, 0]], "o": [[0, 0], [0, 0], [13.188, -22.049], [0, 0], [0, -70.162], [0, 0], [-70.162, 0], [0, 0], [0, 25.36]], "v": [[-106.125, 9.999], [0.264, 190.686], [108.149, 10.315], [128.302, -62.639], [128.302, -62.639], [1.263, -189.679], [1.262, -189.679], [-125.777, -62.639], [-125.777, -62.112]]}], "t": 7}, {"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [{"c": true, "i": [[-8.248, -14.008], [0, 0], [0, 0], [0, 16.469], [0, 0], [44.973, 0], [0, 0], [0, -44.973], [0, 0]], "o": [[0, 0], [0, 0], [8.454, -14.133], [0, 0], [0, -44.973], [0, 0], [-44.973, 0], [0, 0], [0, 16.255]], "v": [[-68.233, 74.502], [-0.039, 190.32], [69.114, 74.705], [82.032, 27.942], [82.032, 27.942], [0.601, -53.489], [0.6, -53.489], [-80.831, 27.942], [-80.831, 28.28]]}], "t": 15}, {"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [{"c": true, "i": [[-8.248, -14.008], [0, 0], [0, 0], [0, 16.469], [0, 0], [44.973, 0], [0, 0], [0, -44.973], [0, 0]], "o": [[0, 0], [0, 0], [8.454, -14.133], [0, 0], [0, -44.973], [0, 0], [-44.973, 0], [0, 0], [0, 16.255]], "v": [[-68.233, 74.502], [-0.039, 190.32], [69.114, 74.705], [82.032, 27.942], [82.032, 27.942], [0.601, -53.489], [0.6, -53.489], [-80.831, 27.942], [-80.831, 28.28]]}], "t": 41}, {"s": [{"c": true, "i": [[-12.867, -21.853], [0, 0], [0, 0], [0, 25.693], [0, 0], [70.162, 0], [0, 0], [0, -70.162], [0, 0]], "o": [[0, 0], [0, 0], [13.188, -22.049], [0, 0], [0, -70.162], [0, 0], [-70.162, 0], [0, 0], [0, 25.36]], "v": [[-106.125, 9.999], [0.264, 190.686], [108.149, 10.315], [128.302, -62.639], [128.302, -62.639], [1.263, -189.679], [1.262, -189.679], [-125.777, -62.639], [-125.777, -62.112]]}], "t": 49}], "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "c": {"a": 0, "k": [0, 0, 0], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 2", "ix": 2, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [{"c": true, "i": [[0, -32.977], [32.977, 0], [0, 32.977], [-32.977, 0]], "o": [[0, 32.977], [-32.977, 0], [0, -32.977], [32.977, 0]], "v": [[59.974, -60.081], [0.264, -0.371], [-59.446, -60.081], [0.264, -119.791]]}], "t": 7}, {"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [{"c": true, "i": [[0, -21.138], [21.138, 0], [0, 21.138], [-21.138, 0]], "o": [[0, 21.138], [-21.138, 0], [0, -21.138], [21.138, 0]], "v": [[38.234, 29.581], [-0.039, 67.855], [-38.313, 29.581], [-0.039, -8.692]]}], "t": 15}, {"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [{"c": true, "i": [[0, -21.138], [21.138, 0], [0, 21.138], [-21.138, 0]], "o": [[0, 21.138], [-21.138, 0], [0, -21.138], [21.138, 0]], "v": [[38.234, 29.581], [-0.039, 67.855], [-38.313, 29.581], [-0.039, -8.692]]}], "t": 41}, {"s": [{"c": true, "i": [[0, -32.977], [32.977, 0], [0, 32.977], [-32.977, 0]], "o": [[0, 32.977], [-32.977, 0], [0, -32.977], [32.977, 0]], "v": [[59.974, -60.081], [0.264, -0.371], [-59.446, -60.081], [0.264, -119.791]]}], "t": 49}], "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 1, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "c": {"a": 0, "k": [0.8157, 0.2157, 0.5176], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}], "ind": 2, "parent": 1}, {"ty": 4, "nm": "Map 3", "sr": 1, "st": 0, "op": 50, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [50, 50, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[196.085, 212.702], [-196.085, 212.702], [-195.489, -209.77], [196.681, -209.77]]}, "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "c": {"a": 0, "k": [0, 0, 0], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "tm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Trim", "nm": "Trim Paths 1", "ix": 2, "e": {"a": 1, "k": [{"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [100], "t": 17}, {"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [100], "t": 38}, {"s": [0], "t": 49}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "s": {"a": 0, "k": 0, "ix": 1}, "m": 2}], "ind": 3, "parent": 1}, {"ty": 4, "nm": "Location mask", "sr": 1, "st": 0, "op": 50, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "td": 1, "ao": 0, "ks": {"a": {"a": 0, "k": [1.262, 190.686, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [51.262, 240.686, 0], "ix": 2}, "r": {"a": 1, "k": [{"o": {"x": 0.37, "y": 0}, "i": {"x": 0.63, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.37, "y": 0}, "i": {"x": 0.63, "y": 1}, "s": [25.508], "t": 7}, {"o": {"x": 0.37, "y": 0}, "i": {"x": 0.63, "y": 1}, "s": [-19.03], "t": 15}, {"o": {"x": 0.37, "y": 0}, "i": {"x": 0.63, "y": 1}, "s": [9.266], "t": 25}, {"o": {"x": 0.37, "y": 0}, "i": {"x": 0.63, "y": 1}, "s": [-5.44], "t": 34}, {"s": [0], "t": 42}], "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [{"c": true, "i": [[-12.867, -21.853], [0, 0], [0, 0], [0, 25.693], [0, 0], [70.162, 0], [0, 0], [0, -70.162], [0, 0]], "o": [[0, 0], [0, 0], [13.188, -22.049], [0, 0], [0, -70.162], [0, 0], [-70.162, 0], [0, 0], [0, 25.36]], "v": [[-106.125, 9.999], [0.264, 190.686], [108.149, 10.315], [128.302, -62.639], [128.302, -62.639], [1.263, -189.679], [1.262, -189.679], [-125.777, -62.639], [-125.777, -62.112]]}], "t": 7}, {"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [{"c": true, "i": [[-8.248, -14.008], [0, 0], [0, 0], [0, 16.469], [0, 0], [44.973, 0], [0, 0], [0, -44.973], [0, 0]], "o": [[0, 0], [0, 0], [8.454, -14.133], [0, 0], [0, -44.973], [0, 0], [-44.973, 0], [0, 0], [0, 16.255]], "v": [[-68.233, 74.502], [-0.039, 190.32], [69.114, 74.705], [82.032, 27.942], [82.032, 27.942], [0.601, -53.489], [0.6, -53.489], [-80.831, 27.942], [-80.831, 28.28]]}], "t": 15}, {"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [{"c": true, "i": [[-8.248, -14.008], [0, 0], [0, 0], [0, 16.469], [0, 0], [44.973, 0], [0, 0], [0, -44.973], [0, 0]], "o": [[0, 0], [0, 0], [8.454, -14.133], [0, 0], [0, -44.973], [0, 0], [-44.973, 0], [0, 0], [0, 16.255]], "v": [[-68.233, 74.502], [-0.039, 190.32], [69.114, 74.705], [82.032, 27.942], [82.032, 27.942], [0.601, -53.489], [0.6, -53.489], [-80.831, 27.942], [-80.831, 28.28]]}], "t": 41}, {"s": [{"c": true, "i": [[-12.867, -21.853], [0, 0], [0, 0], [0, 25.693], [0, 0], [70.162, 0], [0, 0], [0, -70.162], [0, 0]], "o": [[0, 0], [0, 0], [13.188, -22.049], [0, 0], [0, -70.162], [0, 0], [-70.162, 0], [0, 0], [0, 25.36]], "v": [[-106.125, 9.999], [0.264, 190.686], [108.149, 10.315], [128.302, -62.639], [128.302, -62.639], [1.263, -189.679], [1.262, -189.679], [-125.777, -62.639], [-125.777, -62.112]]}], "t": 49}], "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "c": {"a": 0, "k": [0, 0, 0], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 2", "ix": 2, "cix": 2, "np": 1, "it": [{"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 1, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "c": {"a": 0, "k": [0.8157, 0.2157, 0.5176], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0, 0], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}], "ind": 4, "parent": 1}, {"ty": 4, "nm": "Map", "sr": 1, "st": 0, "op": 50, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "tt": 2, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [50, 50, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 2", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[196.085, 179.571], [72.318, 78.345]]}, "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "c": {"a": 0, "k": [0.8157, 0.2157, 0.5176], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 3", "ix": 2, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-56.293, 134.419], [-195.097, 21.304]]}, "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "c": {"a": 0, "k": [0.8157, 0.2157, 0.5176], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 4", "ix": 3, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-81.992, 212.702], [-195.762, 128.696]]}, "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "c": {"a": 0, "k": [0.8157, 0.2157, 0.5176], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 5", "ix": 4, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-56.312, 133.27], [195.736, 24.179]]}, "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "c": {"a": 0, "k": [0.8157, 0.2157, 0.5176], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "tm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Trim", "nm": "Trim Paths 1", "ix": 5, "e": {"a": 1, "k": [{"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [0], "t": 4}, {"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [100], "t": 19.6}, {"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [100], "t": 30}, {"s": [0], "t": 43}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "s": {"a": 0, "k": 0, "ix": 1}, "m": 2}], "ind": 5, "parent": 1}, {"ty": 4, "nm": "Location mask 2", "sr": 1, "st": 0, "op": 50, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "td": 1, "ao": 0, "ks": {"a": {"a": 0, "k": [1.262, 190.686, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [51.262, 240.686, 0], "ix": 2}, "r": {"a": 1, "k": [{"o": {"x": 0.37, "y": 0}, "i": {"x": 0.63, "y": 1}, "s": [0], "t": 0}, {"o": {"x": 0.37, "y": 0}, "i": {"x": 0.63, "y": 1}, "s": [25.508], "t": 7}, {"o": {"x": 0.37, "y": 0}, "i": {"x": 0.63, "y": 1}, "s": [-19.03], "t": 15}, {"o": {"x": 0.37, "y": 0}, "i": {"x": 0.63, "y": 1}, "s": [9.266], "t": 25}, {"o": {"x": 0.37, "y": 0}, "i": {"x": 0.63, "y": 1}, "s": [-5.44], "t": 34}, {"s": [0], "t": 42}], "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 1", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 1, "k": [{"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [{"c": true, "i": [[-12.867, -21.853], [0, 0], [0, 0], [0, 25.693], [0, 0], [70.162, 0], [0, 0], [0, -70.162], [0, 0]], "o": [[0, 0], [0, 0], [13.188, -22.049], [0, 0], [0, -70.162], [0, 0], [-70.162, 0], [0, 0], [0, 25.36]], "v": [[-106.125, 9.999], [0.264, 190.686], [108.149, 10.315], [128.302, -62.639], [128.302, -62.639], [1.263, -189.679], [1.262, -189.679], [-125.777, -62.639], [-125.777, -62.112]]}], "t": 7}, {"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [{"c": true, "i": [[-8.248, -14.008], [0, 0], [0, 0], [0, 16.469], [0, 0], [44.973, 0], [0, 0], [0, -44.973], [0, 0]], "o": [[0, 0], [0, 0], [8.454, -14.133], [0, 0], [0, -44.973], [0, 0], [-44.973, 0], [0, 0], [0, 16.255]], "v": [[-68.233, 74.502], [-0.039, 190.32], [69.114, 74.705], [82.032, 27.942], [82.032, 27.942], [0.601, -53.489], [0.6, -53.489], [-80.831, 27.942], [-80.831, 28.28]]}], "t": 15}, {"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [{"c": true, "i": [[-8.248, -14.008], [0, 0], [0, 0], [0, 16.469], [0, 0], [44.973, 0], [0, 0], [0, -44.973], [0, 0]], "o": [[0, 0], [0, 0], [8.454, -14.133], [0, 0], [0, -44.973], [0, 0], [-44.973, 0], [0, 0], [0, 16.255]], "v": [[-68.233, 74.502], [-0.039, 190.32], [69.114, 74.705], [82.032, 27.942], [82.032, 27.942], [0.601, -53.489], [0.6, -53.489], [-80.831, 27.942], [-80.831, 28.28]]}], "t": 41}, {"s": [{"c": true, "i": [[-12.867, -21.853], [0, 0], [0, 0], [0, 25.693], [0, 0], [70.162, 0], [0, 0], [0, -70.162], [0, 0]], "o": [[0, 0], [0, 0], [13.188, -22.049], [0, 0], [0, -70.162], [0, 0], [-70.162, 0], [0, 0], [0, 25.36]], "v": [[-106.125, 9.999], [0.264, 190.686], [108.149, 10.315], [128.302, -62.639], [128.302, -62.639], [1.263, -189.679], [1.262, -189.679], [-125.777, -62.639], [-125.777, -62.112]]}], "t": 49}], "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "c": {"a": 0, "k": [0, 0, 0], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 2", "ix": 2, "cix": 2, "np": 1, "it": [{"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 1, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "c": {"a": 0, "k": [0.8157, 0.2157, 0.5176], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "fl", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Fill", "nm": "Fill 1", "c": {"a": 0, "k": [1, 0, 0], "ix": 4}, "r": 1, "o": {"a": 0, "k": 100, "ix": 5}}], "ind": 6, "parent": 1}, {"ty": 4, "nm": "Map 2", "sr": 1, "st": 0, "op": 50, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "tt": 2, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [50, 56, 0], "ix": 2}, "r": {"a": 0, "k": 180, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 100, "ix": 11}}, "ef": [], "shapes": [{"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 2", "ix": 1, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[195.21, 178.946], [72.318, 78.345]]}, "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "c": {"a": 0, "k": [0.8157, 0.2157, 0.5176], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 3", "ix": 2, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-56.324, 133.326], [-196.222, 20.179]]}, "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "c": {"a": 0, "k": [0.8157, 0.2157, 0.5176], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 4", "ix": 3, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-77.867, 215.827], [-196.012, 128.321]]}, "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "c": {"a": 0, "k": [0.8157, 0.2157, 0.5176], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "gr", "bm": 0, "hd": false, "mn": "ADBE Vector Group", "nm": "Group 5", "ix": 4, "cix": 2, "np": 2, "it": [{"ty": "sh", "bm": 0, "hd": false, "mn": "ADBE Vector Shape - Group", "nm": "Path 1", "ix": 1, "d": 1, "ks": {"a": 0, "k": {"c": false, "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-56.312, 133.27], [195.736, 24.679]]}, "ix": 2}}, {"ty": "st", "bm": 0, "hd": false, "mn": "ADBE Vector Graphic - Stroke", "nm": "Stroke 1", "lc": 2, "lj": 2, "ml": 1, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 16, "ix": 5, "x": "var $bm_rt;\n$bm_rt = thisComp.layer('Main Stroke width - Color Ctrl').effect('Stroke width')('Slider');"}, "c": {"a": 0, "k": [0.8157, 0.2157, 0.5176], "ix": 3}}, {"ty": "tr", "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "sk": {"a": 0, "k": 0, "ix": 4}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 6}, "sa": {"a": 0, "k": 0, "ix": 5}, "o": {"a": 0, "k": 100, "ix": 7}}]}, {"ty": "tm", "bm": 0, "hd": false, "mn": "ADBE Vector Filter - Trim", "nm": "Trim Paths 1", "ix": 5, "e": {"a": 1, "k": [{"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [0], "t": 4}, {"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [100], "t": 19.6}, {"o": {"x": 0.4, "y": 0}, "i": {"x": 0.6, "y": 1}, "s": [100], "t": 30}, {"s": [0], "t": 43}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "s": {"a": 0, "k": 0, "ix": 1}, "m": 2}], "ind": 7, "parent": 1}, {"ty": 3, "nm": "Zero | troke width - Color Ctrl", "sr": 1, "st": 0, "op": 50, "ip": 0, "hd": false, "ddd": 0, "bm": 0, "hasMask": false, "ao": 0, "ks": {"a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}, "sk": {"a": 0, "k": 0}, "p": {"a": 0, "k": [250, 250, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 10}, "sa": {"a": 0, "k": 0}, "o": {"a": 0, "k": 0, "ix": 11}}, "ef": [], "ind": 8}], "v": "4.8.0", "fr": 25, "op": 50, "ip": 0, "assets": []}