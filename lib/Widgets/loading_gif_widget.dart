import 'package:flutter/material.dart';

class LoadingGifWidget extends StatelessWidget {
  final String gifPath;
  final double width;
  final double height;

  const LoadingGifWidget({
    Key? key,
    required this.gifPath,
    this.width = 250,
    this.height = 250,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
        ),
        child: Image.asset(
          gifPath,
          fit: BoxFit.contain,
          gaplessPlayback: true,
          filterQuality: FilterQuality.high,
        ),
      ),
    );
  }
}
