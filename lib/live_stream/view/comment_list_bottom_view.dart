import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:groceryboouser/Screens/Chatting/View/chatting_screen_p3.dart';
import 'package:groceryboouser/Styles/my_colors.dart';
import 'package:groceryboouser/Styles/my_icons.dart';
import 'package:groceryboouser/Utilities/Constant.dart';
import 'package:groceryboouser/live_stream/widget/comment_list_widget.dart';

class CommentListBottomView extends StatefulWidget {
  const CommentListBottomView({super.key});

  @override
  State<CommentListBottomView> createState() => _CommentListBottomViewState();
}

class _CommentListBottomViewState extends State<CommentListBottomView> {
  final textController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ListView.separated(
        separatorBuilder: (context, index) => const SizedBox(height: 16),
        itemCount: 15,
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(
          vertical: 16,
          horizontal: 16,
        ),
        itemBuilder: (context, index) {
          return CommentListWidget();
        },
      ),
      bottomNavigationBar: Container(
        margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 16.w),
        decoration: BoxDecoration(
          color: add_grey_f6f6f6,
          borderRadius: BorderRadius.circular(56.r),
          // border: Border.all(color: grey_e8e8ea, width: 1)
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            Expanded(
              child: TextField(
                controller: textController,
                style: TextStyle(color: black_1d1929, fontFamily: sf_pro_display_semibold, fontSize: 14.sp),
                decoration: new InputDecoration(
                  isDense: true,
                  hintText: "Type comment here...",
                  hintStyle: TextStyle(color: grey_600, fontFamily: switzer_reuglar, fontSize: 14.sp),
                  border: InputBorder.none,
                ),
                keyboardType: TextInputType.multiline,
                maxLines: 3,
                minLines: 1,
                inputFormatters: [NoLeadingSpaceFormatter()],
              ),
            ),
            InkResponse(
              onTap: () {},
              child: SvgPicture.asset(shareNewPinIcon),
            )
          ],
        ),
      ),
    );
  }
}
