import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:groceryboouser/Layouts/ElevatedGreenButton.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_colors.dart';
import 'package:groceryboouser/live_stream/view/add_new_product_screen.dart';
import 'package:groceryboouser/live_stream/widget/products_widget.dart';
import 'package:responsive_builder/responsive_builder.dart';

class ProductListScreen extends StatefulWidget {
  const ProductListScreen({super.key});

  @override
  State<ProductListScreen> createState() => _ProductListScreenState();
}

class _ProductListScreenState extends State<ProductListScreen> {
  final selectedProductsList = ValueNotifier<List<int>>([]);
  bool isTablet = false;
  bool isLandscape = false;
  @override
  Widget build(BuildContext context) {
    isTablet = getValueForScreenType<bool>(
      context: context,
      mobile: false,
      tablet: true,
    );
    isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    return Scaffold(
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).push(MaterialPageRoute(builder: (context) => const AddNewProductScreen()));
        },
        backgroundColor: pink_D13784,
        child: const Icon(
          Icons.add,
          color: AppColors.white,
        ),
      ),
      appBar: AppBar(
        centerTitle: false,
        backgroundColor: AppColors.white,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.blackColor),
          onPressed: () async {
            if (context.mounted) {
              Navigator.of(context).pop();
            }
          },
        ),
        actions: [
          IconButton(
            padding: EdgeInsets.zero,
            icon: const Icon(Icons.more_vert),
            color: Colors.black,
            onPressed: () {},
          ),
        ],
        title: Text(
          "Products",
          style: AppTextStyle.mediumRegular.copyWith(
            fontSize: 18.sp,
          ),
        ),
      ),
      body: ValueListenableBuilder(
        valueListenable: selectedProductsList,
        builder: (context, selectedProducts, _) {
          return ListView.separated(
              itemBuilder: (context, index) {
                return ProductsWidget(
                  isSelected: selectedProducts.contains(index),
                  onTap: () {
                    if (selectedProducts.contains(index)) {
                      selectedProductsList.value =
                          selectedProductsList.value.where((element) => element != index).toList();
                      return;
                    }
                    selectedProductsList.value = [...selectedProducts, index];
                  },
                );
              },
              padding: const EdgeInsets.symmetric(vertical: 16),
              separatorBuilder: (context, index) {
                return const Column(
                  children: [
                    SizedBox(
                      height: 8,
                    ),
                    Divider(
                      thickness: 1,
                    ),
                    SizedBox(
                      height: 8,
                    ),
                  ],
                );
              },
              itemCount: 5);
        },
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: (Platform.isIOS) ? 24 : 16),
        child: CommonElevatedButton(
          'Add Products',
          AppColors.white,
          pink_d03784,
          () {
            Navigator.of(context).pop();
          },
        ),
      ),
    );
  }
}
