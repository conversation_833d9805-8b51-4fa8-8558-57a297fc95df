import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:groceryboouser/Party_Phase/utility/utility.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_icons.dart';
import 'package:groceryboouser/live_stream/view/stream_history_detail_screen.dart';

class HistoryTab extends StatefulWidget {
  const HistoryTab({super.key});

  @override
  State<HistoryTab> createState() => _HistoryTabState();
}

class _HistoryTabState extends State<HistoryTab> {
  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      separatorBuilder: (context, index) => const Padding(
        padding: EdgeInsets.symmetric(horizontal: 18.0),
        child: Divider(
          thickness: 1,
        ),
      ),
      itemCount: 10,
      itemBuilder: (context, index) {
        return historyItemView();
      },
    );
  }

  Widget historyItemView() {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(MaterialPageRoute(
          builder: (context) => const StreamHistoryDetailScreen(),
        ));
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        decoration: BoxDecoration(
          // color: Colors.black12,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
            child: SizedBox(
              height: 75,
              width: 75,
              child: UtilityForParty.imageLoader(
                url:
                    'https://media.istockphoto.com/id/1223383996/photo/chef-tossing-flaming-vegetable.jpg?s=612x612&w=0&k=20&c=6K9jPlnfL6Rdf16-08RaK6DRfP03wy8pSZviicDpuVQ=',
                placeholder: place_holder,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Padding(
              padding: const EdgeInsets.only(
                left: 8,
                top: 4,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "Burger",
                    style: AppTextStyle.mediumRegular.copyWith(
                      fontSize: 18.sp,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2.0),
                    child: Text(
                      "21/08/2025",
                      style: AppTextStyle.mediumRegular.copyWith(
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                  Text(
                    "Total Earnings: \$12",
                    style: AppTextStyle.mediumRegular.copyWith(
                      fontSize: 14.sp,
                    ),
                  ),
                ],
              ))
        ]),
      ),
    );
  }
}
