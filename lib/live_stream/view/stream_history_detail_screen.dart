import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:groceryboouser/Party_Phase/utility/utility.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_icons.dart';

class StreamHistoryDetailScreen extends StatefulWidget {
  const StreamHistoryDetailScreen({super.key});

  @override
  State<StreamHistoryDetailScreen> createState() => _StreamHistoryDetailScreenState();
}

class _StreamHistoryDetailScreenState extends State<StreamHistoryDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: AppColors.white,
        elevation: 1,
        centerTitle: false,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.blackColor),
          onPressed: () async {
            if (context.mounted) {
              Navigator.of(context).pop();
            }
          },
        ),
        actions: [
          IconButton(
            padding: EdgeInsets.zero,
            icon: const Icon(Icons.more_vert),
            color: Colors.black,
            onPressed: () {},
          ),
        ],
        title: Text(
          "Title",
          style: AppTextStyle.mediumRegular.copyWith(
            fontSize: 18.sp,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              height: 16,
            ),
            Container(
              margin: const EdgeInsets.symmetric(vertical: 4),
              decoration: BoxDecoration(
                // color: Colors.black12,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                  child: SizedBox(
                    height: 75,
                    width: 75,
                    child: UtilityForParty.imageLoader(
                      url:
                          'https://media.istockphoto.com/id/1223383996/photo/chef-tossing-flaming-vegetable.jpg?s=612x612&w=0&k=20&c=6K9jPlnfL6Rdf16-08RaK6DRfP03wy8pSZviicDpuVQ=',
                      placeholder: place_holder,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Padding(
                    padding: const EdgeInsets.only(
                      left: 8,
                      top: 4,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Burger",
                          style: AppTextStyle.mediumRegular.copyWith(
                            fontSize: 18.sp,
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 2.0),
                          child: Text(
                            "21/08/2025",
                            style: AppTextStyle.mediumRegular.copyWith(
                              fontSize: 14.sp,
                            ),
                          ),
                        ),
                        Text(
                          "Total Earnings: \$12",
                          style: AppTextStyle.mediumRegular.copyWith(
                            fontSize: 14.sp,
                          ),
                        ),
                      ],
                    ))
              ]),
            ),
            const Divider(
              thickness: 1,
            ),
            Text("Users",
                style: AppTextStyle.mediumRegular.copyWith(
                  fontSize: 18.sp,
                )),
            ListView.separated(
                separatorBuilder: (context, index) => const Divider(
                      thickness: 1,
                    ),
                itemCount: 4,
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  return userItemView();
                })
          ],
        ),
      ),
    );
  }

  Widget userItemView() {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(MaterialPageRoute(
          builder: (context) => const StreamHistoryDetailScreen(),
        ));
      },
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 4),
        decoration: BoxDecoration(
          // color: Colors.black12,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
            child: SizedBox(
              height: 75,
              width: 75,
              child: UtilityForParty.imageLoader(
                url:
                    'https://images.unsplash.com/photo-1633332755192-727a05c4013d?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D',
                placeholder: place_holder,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Padding(
              padding: const EdgeInsets.only(
                left: 8,
                top: 4,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "John Doe",
                    style: AppTextStyle.mediumRegular.copyWith(
                      fontSize: 18.sp,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2.0),
                    child: Text(
                      "Purchased on: 21/08/2025",
                      style: AppTextStyle.mediumRegular.copyWith(
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                  Text(
                    "Address : 123 Main St, Anytown, USA",
                    style: AppTextStyle.mediumRegular.copyWith(
                      fontSize: 14.sp,
                    ),
                  ),
                ],
              ))
        ]),
      ),
    );
  }
}
