import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:groceryboouser/live_stream/view/comment_list_bottom_view.dart';
import 'package:groceryboouser/live_stream/widget/icon_count_widget.dart';

class LiveStreamIconsView extends StatefulWidget {
  const LiveStreamIconsView({super.key, this.onTap});
  final VoidCallback? onTap;
  @override
  State<LiveStreamIconsView> createState() => _LiveStreamIconsViewState();
}

class _LiveStreamIconsViewState extends State<LiveStreamIconsView> {
  @override
  Widget build(BuildContext context) {
    return Column(children: [
      IconCountWidget(
        icon: Icons.remove_red_eye,
        count: 10,
        onTap: () {},
      ),
      Si<PERSON><PERSON><PERSON>(height: 10.h),
      IconCountWidget(
        icon: Icons.favorite,
        count: 10,
        onTap: () {},
      ),
      <PERSON><PERSON><PERSON><PERSON>(height: 10.h),
      IconCountWidget(
        icon: Icons.chat,
        count: 10,
        onTap: () {
          _openBottomSheet();
        },
      ),
      Sized<PERSON><PERSON>(height: 10.h),
      IconCountWidget(
        icon: Icons.production_quantity_limits_sharp,
        count: 10,
        onTap: widget.onTap,
      ),
    ]);
  }

  void _openBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          children: [
            // Drag handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Content
            Expanded(
              child: CommentListBottomView(),
            ),
          ],
        ),
      ),
    );
  }
}
