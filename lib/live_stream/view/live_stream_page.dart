import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:groceryboouser/Party_Phase/utility/utility.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_colors.dart';
import 'package:groceryboouser/Styles/my_icons.dart';
import 'package:groceryboouser/live_stream/view/live_stream_list_view.dart';
import 'package:groceryboouser/live_stream/view/video_stream_list_view.dart';

// appBar: AppBar(
//           elevation: 0,
//           leadingWidth: 40,
//           backgroundColor: AppColors.white,
//           title: Text(
//             "Search Livestream",
//             style: AppTextStyle.largeMedium,
//           ),
//           leading: InkWell(
//             onTap: () {
//               Navigator.pop(context);
//             },
//             child: Padding(
//               padding: const EdgeInsets.only(left: 22),
//               child: SvgPicture.asset(
//                 icon_new_back_Arrow,
//                 // width: 20.w,
//                 // height: 20.h,
//               ),
//             ),
//           ),
//           bottom: TabBar(
//             labelColor: Colors.black,
//             unselectedLabelColor: Colors.grey,
//             indicatorColor: Colors.blue,
//             tabs: [
//               Tab(text: "Italian"),
//               Tab(text: "Indian"),
//               Tab(text: "Mexican"),
//             ],
//           ),
//         ),
class SearchLiveStreamPage extends StatefulWidget {
  const SearchLiveStreamPage({super.key});

  @override
  State<SearchLiveStreamPage> createState() => _SearchLiveStreamPageState();
}

class _SearchLiveStreamPageState extends State<SearchLiveStreamPage> {
  final List<String> categories = [
    'Italian',
    'Indian',
    'Mexican',
    'Chinese',
    'Thai',
    'Vietnamese',
    'Japanese',
    'Korean',
    'French',
    'German',
    'Spanish',
    'Portuguese',
  ];
  int selectedCategoryIndex = 0;
  final searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2, // Live Stream, Video Stream
      child: Scaffold(
        appBar: AppBar(
          elevation: 0,
          leadingWidth: 40,
          backgroundColor: AppColors.white,
          title: Text(
            "Search Livestream",
            style: AppTextStyle.largeMedium,
          ),
          leading: InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: Padding(
              padding: const EdgeInsets.only(left: 22),
              child: SvgPicture.asset(
                icon_new_back_Arrow,
                // width: 20.w,
                // height: 20.h,
              ),
            ),
          ),
        ),
        body: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            // Search Bar
            Container(
              padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
              margin: EdgeInsets.only(top: 8.h, bottom: 16.h, left: 16.w, right: 16.w),
              decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(8.r)), color: grey_50),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  SvgPicture.asset(
                    iconSearchGrey,
                    width: 16.w,
                    height: 16.h,
                    color: Colors.black,
                  ),
                  SizedBox(
                    width: 14.w,
                  ),
                  Expanded(
                    child: TextField(
                      style: AppTextStyle.smallRegular.copyWith(color: grey_600),
                      decoration: new InputDecoration(
                        isDense: true,
                        contentPadding: EdgeInsets.zero,
                        hintText: "Search Livestream...",
                        hintStyle: AppTextStyle.smallRegular.copyWith(color: grey_600),
                        labelStyle: AppTextStyle.smallRegular,
                        border: InputBorder.none,
                      ),
                      controller: searchController,
                      keyboardType: TextInputType.text,
                      textInputAction: TextInputAction.done,
                      onChanged: (val) {},
                    ),
                  ),
                ],
              ),
            ),

            // Horizontal Image + Category List
            Container(
              alignment: Alignment.centerLeft,
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(width: 16), // Add left padding
                    ...categories
                        .map((e) => categoryView(
                              onTap: () {},
                              image: e,
                              title: e,
                            ))
                        .toList(),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            // Inner TabBar for Live Stream & Video Stream
            TabBar(
              labelColor: AppColors.blackColor,
              unselectedLabelColor: AppColors.textMuted,
              indicatorColor: AppColors.blackColor,
              labelStyle: AppTextStyle.smallMedium,
              tabs: [
                Tab(
                  text: "Live Stream",
                ),
                Tab(text: "Video Stream"),
              ],
            ),

            // Tab Views
            Expanded(
              child: TabBarView(
                children: [
                  StreamListView(),
                  VideoStreamListView(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget categoryView({String? image, String? title, VoidCallback? onTap}) {
    // Use a placeholder image or a default icon since we don't have actual image URLs
    final imageWidget = image?.isNotEmpty == true
        ? UtilityForParty.imageLoader(
            url: 'https://via.placeholder.com/62?text=${Uri.encodeComponent(title ?? '')}',
            placeholder: grey_bg,
            isShapeCircular: true,
            shape: BoxShape.circle,
          )
        : Container(
            height: 62.h,
            width: 62.w,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.grey[200],
            ),
            child: Icon(Icons.category, size: 30, color: Colors.grey[600]),
          );

    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.only(right: 15.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 62.h,
              width: 62.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
              ),
              child: imageWidget,
            ),
            SizedBox(height: 7.h),
            Text(
              title ?? '',
              overflow: TextOverflow.ellipsis,
              style: AppTextStyle.xSmallMedium,
            )
          ],
        ),
      ),
    );
  }
}
