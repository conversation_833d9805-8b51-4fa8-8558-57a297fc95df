import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gif_view/gif_view.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/my_icons.dart';
import 'package:groceryboouser/live_stream/view/live_stream_icons_view.dart';
import 'package:groceryboouser/live_stream/widget/product_list_widget.dart';

class LiveStreamPage extends StatefulWidget {
  const LiveStreamPage({super.key});

  @override
  State<LiveStreamPage> createState() => _LiveStreamPageState();
}

class _LiveStreamPageState extends State<LiveStreamPage> {
  bool showProduct = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Container(
              height: MediaQuery.of(context).size.height,
              width: double.infinity,
              // color: AppColors.grey,
              child: GifView.asset(
                'assets/splash_video.gif',
                loop: true,
                fit: BoxFit.cover,
                autoPlay: true,
                onFinish: () {},
                // frameRate: 30,
              )),
          Positioned(
            left: 16.w,
            top: 53.h,
            child: InkWell(
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                padding: EdgeInsets.all(14.r),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.black.withOpacity(0.4),
                ),
                child: SvgPicture.asset(
                  icon_new_back_Arrow,
                  color: AppColors.white,
                ),
              ),
            ),
          ),
          Positioned(
            bottom: showProduct ? 180.h : 60.h,
            right: 10.w,
            child: LiveStreamIconsView(
              onTap: () {
                setState(() {
                  showProduct = !showProduct;
                });
              },
            ),
          ),
          if (showProduct)
            Container(
              height: 130.h,
              margin: EdgeInsets.symmetric(vertical: 30),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                primary: false,
                shrinkWrap: true,
                padding: EdgeInsets.symmetric(horizontal: 16.w),
                itemCount: 4,
                itemBuilder: (context, i) => ProductListWidget(onTap: () {}),
              ),
            ),
        ],
      ),
    );
  }
}
