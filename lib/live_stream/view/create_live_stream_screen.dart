import 'dart:developer';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:groceryboouser/Layouts/CustomTextFiled.dart';
import 'package:groceryboouser/Layouts/ElevatedGreenButton.dart';
import 'package:groceryboouser/Screens/Authentication/SignIn/model/SigninModel.dart';
import 'package:groceryboouser/Screens/Location/controller/api_manager.dart';
import 'package:groceryboouser/Screens/Networks/api_endpoint.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_colors.dart';
import 'package:groceryboouser/Styles/my_icons.dart';
import 'package:groceryboouser/Styles/my_strings.dart';
import 'package:groceryboouser/Utilities/Constant.dart';
import 'package:groceryboouser/Utils/preference_utils.dart';
import 'package:groceryboouser/Utils/share_predata.dart';
import 'package:groceryboouser/Utils/text_utilities.dart';
import 'package:groceryboouser/live_stream/model/live_stream_model.dart';
import 'package:groceryboouser/live_stream/view/product_list_screen.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:mime/mime.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:video_player/video_player.dart';

class CreateLiveStreamScreen extends StatefulWidget {
  const CreateLiveStreamScreen({super.key, this.liveStream = false});
  final bool? liveStream;

  @override
  State<CreateLiveStreamScreen> createState() => _CreateLiveStreamScreenState();
}

class _CreateLiveStreamScreenState extends State<CreateLiveStreamScreen> {
  TextEditingController titleController = TextEditingController();
  TextEditingController discritionController = TextEditingController();
  TextEditingController categoryController = TextEditingController();
  TextEditingController dateTimeController = TextEditingController();
  TextEditingController priceController = TextEditingController();
  List<String> productList = [
    "Product 1",
    "Product 2",
    "Product 3",
    "Product 4",
    "Product 5",
    "Product 6",
    "Product 7",
    "Product 8",
  ];
  String? textFieldFocused;

  String starttime = "Select date & time";
  DateTime? selectedDateTime;

  final ImagePicker _picker = ImagePicker();

  File? _video;
  String pickImageExtension = "";
  String? videoPath;
  File? _videoMain;
  String? videoMainPath;
  bool isPlaying = false;
  VideoPlayerController? controller;
  bool isTablet = false;
  bool isLandscape = false;

  Future<void> _pickVideo(bool isMainVideo) async {
    final pickedFile = await _picker.pickMedia();
    log(pickedFile?.path ?? "");
    if (pickedFile != null) {
      final mimeType = lookupMimeType(pickedFile.path);
      final isImage = !(mimeType?.startsWith('video/') ?? false);

      if (isImage) {
        Utility.toast(message: 'Please select video');
      } else {
        if (isMainVideo) {
          videoMainPath = pickedFile.name;
          _videoMain = File(pickedFile.path);
          log(_videoMain!.path);
          setState(() {});
        } else {
          pickImageExtension = 'mp4';
          setState(() {
            _video = File(pickedFile.path);
            // generalInformationController.pickImageExtension = 'mp4';

            videoPath = _video!.path;
            controller = VideoPlayerController.file(_video!)
              ..initialize().then((_) async {
                log('asdasdasd');
                controller?.setVolume(0);

                isPlaying = true;
                await Future.delayed(const Duration(milliseconds: 500));
                setState(() {});
              });
            controller?.addListener(setPlayingData);
            log(isPlaying.toString());
            _notify();
          });
        }
      }

      log(pickedFile.path);

      setState(() {});
    }
  }

  void setPlayingData() {
    isPlaying = controller!.value.isPlaying;
  }

  _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    isTablet = getValueForScreenType<bool>(
      context: context,
      mobile: false,
      tablet: true,
    );
    isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(49.0),
        child: AppBar(
          centerTitle: true,
          elevation: 0,
          backgroundColor: white_ffffff,
          leading: InkWell(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Container(
              width: 45,
              height: 45,
              decoration: BoxDecoration(
                color: Colors.transparent,
              ),
              child: SvgPicture.asset(
                icon_back_black_party,
                width: 24,
                height: 24,
              ),
            ),
          ),
          title: Text(
            !(widget.liveStream ?? false) ? "Create Live Stream" : 'Create Video Stream',
            style: AppTextStyle.largeMedium,
          ),
        ),
      ),
      bottomSheet: Padding(
          padding: EdgeInsets.only(left: 16.w, bottom: 20.h, right: 16.w),
          child: CommonElevatedButton(
              !(widget.liveStream ?? false) ? "Create Live Stream" : 'Create Video Stream', Colors.white, black_121212,
              () {
            createLiveStream();
          })),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: Get.width * 0.061.w),
              Padding(
                padding: EdgeInsets.only(left: 0),
                child: Text(
                  "Upload Cover Video",
                  style: AppTextStyle.smallMedium,
                ),
              ),
              SizedBox(height: Get.width * 0.011.w),
              if (controller != null)
                Padding(
                  padding: const EdgeInsets.only(left: 0),
                  child: GestureDetector(
                    onTap: () {
                      _pickVideo(false);
                    },
                    child: Container(
                      height: Get.width * 0.9.w,
                      width: Get.width * 0.51.w,
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(
                          Radius.circular(8),
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: VideoPlayer(controller!),
                      ),
                    ),
                  ),
                )
              else
                GestureDetector(
                  onTap: () {
                    _pickVideo(false);
                  },
                  child: Container(
                    width: double.infinity,
                    height: 162.h,
                    decoration: BoxDecoration(color: AppColors.greylow),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SvgPicture.asset(icon_add_image_latest),
                        SizedBox(
                          height: 16.h,
                        ),
                        Text("Add Video",
                            style: AppTextStyle.smallRegular.copyWith(color: grey_500), textAlign: TextAlign.left),
                      ],
                    ),
                  ),
                ),
              SizedBox(height: Get.width * 0.061.w),
              Text(
                'Title',
                textAlign: TextAlign.left,
                style: AppTextStyle.smallMedium,
              ),
              SizedBox(
                height: 8.h,
              ),
              CustomTextFiled(
                onChanged: (p0) {},
                inputType: TextInputType.text,
                controller: titleController,
                onTap: () {
                  setState(() {
                    textFieldFocused = "title";
                  });
                },

                textInputAction: TextInputAction.next,
                // borderColor: textFieldFocused == "title" ? AppColors.primaryColor : AppColors.borderColor,
                // hint: 'Title',
              ),
              SizedBox(height: Get.width * 0.061.w),
              Text(
                'Description',
                textAlign: TextAlign.left,
                style: AppTextStyle.smallMedium,
              ),
              SizedBox(
                height: 8.h,
              ),
              CustomTextFiled(
                onChanged: (p0) {},
                inputType: TextInputType.text,
                controller: discritionController,
                onTap: () {
                  setState(() {
                    textFieldFocused = "description";
                  });
                },
                maxLine: 3,
                textInputAction: TextInputAction.next,
              ),
              SizedBox(height: Get.width * 0.061.w),
              Text(
                'Category',
                textAlign: TextAlign.left,
                style: AppTextStyle.smallMedium,
              ),
              SizedBox(
                height: 8.h,
              ),
              CustomTextFiled(
                onChanged: (p0) {},
                inputType: TextInputType.text,
                controller: categoryController,
                onTap: () {
                  setState(() {
                    textFieldFocused = "category";
                  });
                },
                textInputAction: TextInputAction.next,
              ),
              SizedBox(height: Get.width * 0.061.w),
              // Text(
              //   'Date Time',
              //   textAlign: TextAlign.left,
              //   style: AppTextStyle.smallMedium,
              // ),
              // SizedBox(
              //   height: 8.h,
              // ),
              Text(
                dateandtime,
                style: AppTextStyle.smallMedium,
              ),
              SizedBox(
                height: 8.h,
              ),
              GestureDetector(
                onTap: () {
                  selectTimePicker(context);
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 13.h),
                  decoration: BoxDecoration(borderRadius: BorderRadius.circular(8.r), color: grey_e8e8e8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Text
                      Text(starttime, style: AppTextStyle.mediumRegular, textAlign: TextAlign.left),
                      SvgPicture.asset(
                        icon_calendar_latest_party,
                        width: 20,
                        height: 20,
                      )
                    ],
                  ),
                ),
              ),
              // CustomTextFiled(
              //   onChanged: (p0) {},
              //   inputType: TextInputType.text,
              //   controller: dateTimeController,
              //   onTap: () {
              //     setState(() {
              //       textFieldFocused = "dateTime";
              //     });
              //   },
              //   textInputAction: TextInputAction.next,
              // ),
              SizedBox(height: Get.width * 0.061.w),
              Text(
                'Price',
                textAlign: TextAlign.left,
                style: AppTextStyle.smallMedium,
              ),
              SizedBox(
                height: 8.h,
              ),
              CustomTextFiled(
                onChanged: (p0) {},
                inputType: TextInputType.number,
                controller: priceController,
                onTap: () {
                  setState(() {
                    textFieldFocused = "price";
                  });
                },
                textInputAction: TextInputAction.next,
              ),
              SizedBox(height: Get.width * 0.061.w),
              Text(
                'Product',
                textAlign: TextAlign.left,
                style: AppTextStyle.smallMedium,
              ),
              SizedBox(
                height: 8.h,
              ),
              CustomTextFiled(
                onChanged: (p0) {},
                inputType: TextInputType.text,
                controller: TextEditingController(text: 'Product'),
                onTap: () {
                  setState(() {
                    textFieldFocused = "product";
                  });
                  Navigator.push(context, MaterialPageRoute(builder: (context) => const ProductListScreen()));
                },
                textInputAction: TextInputAction.next,
              ),
              SizedBox(height: Get.width * 0.15.w),
              SizedBox(height: Platform.isIOS ? 24 : 16),
            ],
          ),
        ),
      ),
    );
  }

  void selectTimePicker(BuildContext context) {
    showModalBottomSheet(
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        context: context,
        builder: (
          BuildContext context,
        ) {
          return Container(
              //  margin: EdgeInsets.all(20.r),
              decoration: BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(13.r),
                  topRight: Radius.circular(13.r),
                  // bottomRight: Radius.circular(13.r),
                  // bottomLeft: Radius.circular(13.r)
                ),
              ),
              child: SingleChildScrollView(
                padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(13)),
                      child: Column(children: [
                        Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(top: 20),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Daily Opportunities
                                  // Title
                                  Padding(
                                    padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 20.h),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text("Select Date & Time",
                                            style: TextStyle(
                                                color: Colors.black,
                                                fontFamily: switzer_bold,
                                                fontStyle: FontStyle.normal,
                                                fontSize: 16.sp),
                                            textAlign: TextAlign.center),
                                        GestureDetector(
                                            onTap: () {
                                              Navigator.pop(context, false);
                                            },
                                            child: SvgPicture.asset(iconSmallCancel))
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const Divider(
                              height: 1,
                              thickness: 1,
                              color: grey_100,
                            ),
                            SizedBox(
                              height: 12.h,
                            ),
                            SizedBox(
                              height: 180.h,
                              child: CupertinoDatePicker(
                                //  initialDateTime:
                                mode: CupertinoDatePickerMode.date,
                                // use24hFormat: false,

                                // This is called when the user changes the time.
                                onDateTimeChanged: (DateTime newTime) {
                                  String yourDateTime = DateFormat.yMd().format(newTime);
                                  setState(() {
                                    selectedDateTime = newTime;
                                    starttime = yourDateTime;
                                  });
                                },
                              ),
                            ),
                            Center(
                              child: GestureDetector(
                                onTap: () {
                                  Navigator.pop(context, false);
                                },
                                child: Container(
                                  width: double.infinity,
                                  color: Colors.transparent,
                                  child: Padding(
                                    padding: const EdgeInsets.all(20.0),
                                    child: Text("Confirm".toUpperCase(),
                                        style: TextStyle(
                                            color: Colors.black,
                                            fontWeight: FontWeight.w500,
                                            fontFamily: switzer_medium,
                                            fontStyle: FontStyle.normal,
                                            fontSize: 15.sp),
                                        textAlign: TextAlign.center),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        )
                      ]),
                    ),
                  ],
                ),
              ));
        });
  }

  Future<void> createLiveStream() async {
    if (await ApiManager.checkInternet()) {
      if (titleController.text.isEmpty) {
        snackBar(context, 'Please enter title'.toString());
        return;
      }
      if (discritionController.text.isEmpty) {
        snackBar(context, 'Please enter description'.toString());
        return;
      }
      if (categoryController.text.isEmpty) {
        snackBar(context, 'Please enter category'.toString());
        return;
      }
      if (dateTimeController.text.isEmpty) {
        snackBar(context, 'Please enter date time'.toString());
        return;
      }
      if (priceController.text.isEmpty) {
        snackBar(context, 'Please enter price'.toString());
        return;
      }
      var preferences = MySharedPref();
      final data = await preferences.getSignInModel(SharePreData.key_SaveSignInModel);
      if (data != null) {
        SigninModel? myModel = await preferences.getSignInModel(SharePreData.key_SaveSignInModel);
        var request = <String, String>{};
        request['title'] = titleController.text.trim();
        request['description'] = discritionController.text.trim();
        request['category'] = categoryController.text.trim();
        request['scheduled_at'] = dateTimeController.text.trim();
        request['price'] = priceController.text.trim();
        request['type'] = (widget.liveStream ?? false) ? 'LIVE' : 'RECORDED';
        request['user_id'] = myModel?.data!.id.toString() ?? '';

        final List<MapEntry<String, File>> files = [
          if (_videoMain != null) MapEntry('party_image', _videoMain ?? File('')),
        ];

        CreateLiveStreamResponse response = CreateLiveStreamResponse.fromJson(
            await ApiManager().multipartRequest(url: createLiveStreamApi, request: request, files: files));
        if (response.success == true && response.data != null) {
          snackBar(context, response.message ?? '');
          Navigator.pop(context);
        }
      }
    } else {
      snackBar(context, 'No internet connection'.toString());
    }
  }
}
