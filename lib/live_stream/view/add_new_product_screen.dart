import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:groceryboouser/Layouts/CustomTextFiled.dart';
import 'package:groceryboouser/Layouts/ElevatedGreenButton.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_colors.dart';
import 'package:groceryboouser/Styles/my_icons.dart';
import 'package:groceryboouser/Utils/SizeConfig.dart';
import 'package:groceryboouser/Utils/common_widget.dart';
import 'package:groceryboouser/Widgets/UserProfileDecoration.dart';

class AddNewProductScreen extends StatefulWidget {
  const AddNewProductScreen({super.key});

  @override
  State<AddNewProductScreen> createState() => _AddNewProductScreenState();
}

class _AddNewProductScreenState extends State<AddNewProductScreen> {
  TextEditingController titleController = TextEditingController();
  TextEditingController discritionController = TextEditingController();
  String? textFieldFocused;
  String strImage = "";
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: false,
        backgroundColor: AppColors.white,
        elevation: 1,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.blackColor),
          onPressed: () async {
            if (context.mounted) {
              Navigator.of(context).pop();
            }
          },
        ),
        actions: [
          IconButton(
            padding: EdgeInsets.zero,
            icon: const Icon(Icons.more_vert),
            color: Colors.black,
            onPressed: () {},
          ),
        ],
        title: Text(
          "Add New Product",
          style: AppTextStyle.mediumRegular.copyWith(
            fontSize: 18.sp,
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              children: <Widget>[
                Container(
                    alignment: Alignment.topCenter,
                    child: imagePath.toString() != "File: ''"
                        ? Container(
                            decoration: UserProfileDecoration,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(0.r),
                              child: Image.file(
                                imagePath,
                                // imagePath,
                                fit: BoxFit.fill,
                                width: 80.w,
                                height: 80.h,
                              ),
                            ),
                          )
                        : strImage.isNotEmpty
                            ? Container(
                                decoration: UserProfileDecoration,
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(50.r),
                                  child: CachedNetworkImage(
                                    fit: BoxFit.cover,
                                    maxHeightDiskCache: 150,
                                    maxWidthDiskCache: 150,
                                    alignment: Alignment.center,
                                    width: 80.w,
                                    height: 80.h,
                                    imageUrl: strImage,
                                  ),
                                ),
                              )
                            : Container(
                                decoration: UserProfileDecoration,
                                child: Container(
                                  child: Icon(Icons.image, color: button_grey_AFAFAF, size: 40.sp),
                                ))),
                Container(
                  margin: EdgeInsets.only(left: 203.w, top: SizeConfig.blockSizeVertical! * 6.6.h),
                  child: InkWell(
                    onTap: () {
                      FocusScope.of(context).unfocus();
                      selectPhoto();
                    },
                    child: Container(
                        width: 32.w,
                        height: 32.h,
                        decoration: BoxDecoration(
                          color: white,
                          border: Border.all(color: button_grey_AFAFAF, width: 1.w),
                          shape: BoxShape.circle,
                        ),
                        alignment: Alignment.center,
                        child: SvgPicture.asset(edit_prodile_icon)),
                  ),
                )
              ],
            ),
            Text(
              'Title',
              textAlign: TextAlign.left,
              style: AppTextStyle.smallMedium,
            ),
            SizedBox(
              height: 8.h,
            ),
            CustomTextFiled(
              onChanged: (p0) {},
              inputType: TextInputType.text,
              controller: titleController,
              onTap: () {
                setState(() {
                  textFieldFocused = "title";
                });
              },
              textInputAction: TextInputAction.next,
            ),
            SizedBox(height: Get.width * 0.061.w),
            Text(
              'Description',
              textAlign: TextAlign.left,
              style: AppTextStyle.smallMedium,
            ),
            SizedBox(
              height: 8.h,
            ),
            CustomTextFiled(
              onChanged: (p0) {},
              inputType: TextInputType.text,
              controller: discritionController,
              onTap: () {
                setState(() {
                  textFieldFocused = "discrition";
                });
              },
              textInputAction: TextInputAction.next,
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: (Platform.isIOS) ? 24 : 16),
        child: CommonElevatedButton(
          'Add New Products',
          AppColors.white,
          pink_d03784,
          () {
            Navigator.of(context).pop();
          },
        ),
      ),
    );
  }

  selectPhoto() {
    showImagePicker(context).then((value) {
      setState(() {});
      print(imagePath.toString());
    });
  }
}
