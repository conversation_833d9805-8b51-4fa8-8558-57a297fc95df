import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:groceryboouser/Party_Phase/utility/utility.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_colors.dart';
import 'package:groceryboouser/Styles/my_icons.dart';
import 'package:video_player/video_player.dart';

class BuyStreamPage extends StatefulWidget {
  const BuyStreamPage({super.key});

  @override
  State<BuyStreamPage> createState() => _BuyStreamPageState();
}

class _BuyStreamPageState extends State<BuyStreamPage> {
  VideoPlayerController? videoPlayerController;
  Future? initializeVideoPlayerFuture;

  @override
  void initState() {
    super.initState();
    videoPlayerController = VideoPlayerController.networkUrl(
      Uri.parse(
        'https://flutter.github.io/assets-for-api-docs/assets/videos/butterfly.mp4',
      ),
    )..initialize().then((_) {
        // Ensure the first frame is shown after the video is initialized, even before the play button has been pressed.
        initializeVideoPlayerFuture = videoPlayerController!.initialize();
        videoPlayerController?.setVolume(1);
        videoPlayerController?.play();
        videoPlayerController?.setLooping(true);
      });
  }

  bool isExpanded = false;
  @override
  void dispose() {
    videoPlayerController!.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Container(
          //   child: Stack(
          //     alignment: Alignment.topRight,
          //     children: [
          //       InkWell(
          //         onTap: () {
          //           videoPlayerController!.value.volume == 0
          //               ? videoPlayerController!.setVolume(1)
          //               : videoPlayerController!.setVolume(0);
          //           setState(() {});
          //         },
          //         child: VisibilityDetector(
          //           key: Key('video'),
          //           onVisibilityChanged: (VisibilityInfo info) {
          //             if (info.visibleFraction < 1 && videoPlayerController != null) {
          //               videoPlayerController!.setVolume(0);
          //               videoPlayerController!.pause();
          //               setState(() {});
          //             } else if (info.visibleFraction > 0 && videoPlayerController != null) {
          //               videoPlayerController!.play();
          //               setState(() {});
          //             }
          //           },
          //           child: ClipRRect(
          //             child: FutureBuilder(
          //               future: initializeVideoPlayerFuture,
          //               builder: (context, snapshot) {
          //                 if (snapshot.connectionState == ConnectionState.done) {
          //                   return Center(
          //                     child: Stack(
          //                       alignment: Alignment.center,
          //                       children: [
          //                         AspectRatio(
          //                           aspectRatio: videoPlayerController!.value.aspectRatio,
          //                           child: VideoPlayer(videoPlayerController!),
          //                         ),
          //                       ],
          //                     ),
          //                   );
          //                 } else {
          //                   return SizedBox(
          //                     height: 200,
          //                     child: Center(
          //                       child: CircularProgressIndicator(),
          //                     ),
          //                   );
          //                 }
          //               },
          //             ),
          //           ),
          //         ),
          //       ),
          //       Container(
          //         padding: EdgeInsets.all(10.r),
          //         margin: EdgeInsets.only(top: 53.h, right: 16.w),
          //         decoration: BoxDecoration(
          //           color: Color(0x73000000),
          //           shape: BoxShape.circle,
          //         ),
          //         child: InkWell(
          //           onTap: () {
          //             setState(() {
          //               if (videoPlayerController!.value.isPlaying) {
          //                 videoPlayerController!.pause();
          //               } else {
          //                 videoPlayerController!.play();
          //                 videoPlayerController!.setVolume(1);
          //               }
          //             });
          //           },
          //           child: Icon(
          //             videoPlayerController!.value.isPlaying ? Icons.pause : Icons.play_arrow,
          //             size: 25,
          //             color: Colors.white,
          //           ),
          //         ),
          //       )
          //     ],
          //   ),
          // ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Product Name',
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyle.xLargeBold,
                ),
                SizedBox(
                  height: 4.h,
                ),
                InkWell(
                  onTap: () {
                    _openBottomSheet();
                  },
                  child: Text(
                    'Product Description' * 50,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: AppTextStyle.mediumRegular.copyWith(color: light_black_333333),
                  ),
                ),
                SizedBox(
                  height: 4.h,
                ),
                Text(
                  '12 July 2025',
                  style: AppTextStyle.mediumRegular.copyWith(color: light_black_333333),
                ),
                SizedBox(
                  height: 4.h,
                ),
                Text(
                  '\$1000',
                  style: AppTextStyle.mediumRegular.copyWith(color: light_black_333333),
                ),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.symmetric(horizontal: 16.w),
            width: double.infinity,
            height: 48.h,
            child: ElevatedButton(
                child: Text('Buy', style: AppTextStyle.mediumBold.copyWith(color: AppColors.white)), // Button
                onPressed: () {},
                style: ElevatedButton.styleFrom(
                  foregroundColor: Colors.transparent, backgroundColor: AppColors.primaryColor,
                  elevation: 0,
                  minimumSize: Size.zero, // Set this
                  padding: EdgeInsets.zero,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                )),
          ),
          SizedBox(
            height: 16.h,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              'Products',
              style: AppTextStyle.mediumBold,
            ),
          ),
          SizedBox(
            height: 8.h,
          ),
          Container(
            height: 130.h,
            color: AppColors.white,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              primary: false,
              shrinkWrap: true,
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              itemCount: 4,
              itemBuilder: (context, i) => InkWell(
                onTap: () async {},
                child: Container(
                  margin: EdgeInsets.only(right: 8.w),
                  width: 104.w,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        height: 100.h,
                        width: 104.w,
                        child: UtilityForParty.imageLoader(
                          url: '',
                          placeholder: grey_bg,
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      SizedBox(
                        height: 8.h,
                      ),
                      Text(
                        'Product Name',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: AppTextStyle.smallMedium.copyWith(
                          color: AppColors.blackColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _openBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.only(bottom: 16),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Description',
                  style: AppTextStyle.largeBold,
                ),
                InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Icon(Icons.close))
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: SingleChildScrollView(
                child: Text(
                  'Product Description' * 100,
                  style: AppTextStyle.mediumRegular.copyWith(color: light_black_333333),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
