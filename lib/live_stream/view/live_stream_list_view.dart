import 'package:flutter/material.dart';
import 'package:groceryboouser/live_stream/view/buy_stream_page.dart';
import 'package:groceryboouser/live_stream/widget/product_widget.dart';

class StreamListView extends StatelessWidget {
  const StreamListView({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      separatorBuilder: (context, index) => const SizedBox(height: 16),
      padding: const EdgeInsets.all(16),
      itemCount: 3,
      itemBuilder: (_, index) {
        return ProductWidget(
            index: index,
            viewOnPressed: () {},
            buyOnPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => BuyStreamPage(),
                ),
              );
            });
      },
    );
  }
}
