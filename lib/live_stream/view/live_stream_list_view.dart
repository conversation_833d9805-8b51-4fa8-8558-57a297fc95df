import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:groceryboouser/Screens/Location/controller/api_manager.dart';
import 'package:groceryboouser/Screens/Networks/api_endpoint.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/my_colors.dart';
import 'package:groceryboouser/Styles/my_icons.dart';
import 'package:groceryboouser/Utils/text_utilities.dart';
import 'package:groceryboouser/live_stream/model/live_stream_list_model.dart';
import 'package:groceryboouser/live_stream/view/buy_stream_page.dart';
import 'package:groceryboouser/live_stream/view/live_stream_page.dart';
import 'package:groceryboouser/live_stream/widget/product_widget.dart';
import 'package:visibility_detector/visibility_detector.dart';

class LiveStreamView extends StatefulWidget {
  const LiveStreamView({
    super.key,
  });

  @override
  State<LiveStreamView> createState() => _LiveStreamViewState();
}

class _LiveStreamViewState extends State<LiveStreamView> {
  int page = 0;
  int perPage = 10;
  bool stop = false;
  List<LiveStreamModel> liveStreamList = [];
  ValueNotifier<bool> isLoading = ValueNotifier<bool>(false);
  ValueNotifier<bool> isPageLoading = ValueNotifier<bool>(false);

  @override
  void initState() {
    super.initState();
    getStreamList(isFirst: true);
  }

  _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  void noDataLogic(pageNumber) {
    page = pageNumber - 1;
    stop = true;
    _notify();
  }

  _refresh() {
    liveStreamList.clear();
    page = 0;
    stop = false;
    _notify();
    getStreamList(isFirst: true);
  }

  Future<void> getStreamList({required bool isFirst}) async {
    if (isFirst) {
      isLoading.value = true;
    } else {
      isPageLoading.value = true;
    }
    if (await ApiManager.checkInternet()) {
      page += 1;
      var request = <String, dynamic>{};
      request['page'] = page.toString();
      request['per_page'] = perPage.toString();
      request['user_id'] = '3047'.toString();
      request['type'] = 'LIVE';
      LiveStreamListResponse response = LiveStreamListResponse.fromJson(
        await ApiManager().getCall(liveStreamListApi, request, context),
      );
      log(response.toJson().toString());
      if (response.statusCode == 200 &&
          response.success == true &&
          response.data != null &&
          response.data!.isNotEmpty) {
        log('AAAAAAA');
        liveStreamList.addAll(response.data!);

        _notify();
        if (isFirst) {
          isLoading.value = false;
        } else {
          isPageLoading.value = false;
        }
      } else {
        noDataLogic(page);

        if (isFirst) {
          isLoading.value = false;
        } else {
          isPageLoading.value = false;
        }
      }
    } else {
      if (isFirst) {
        isLoading.value = false;
      } else {
        isPageLoading.value = false;
      }
      Utility.toast(message: 'No Internet Connection');
    }
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
        color: pink_D13784,
        onRefresh: () async {
          _refresh();
        },
        child: Stack(children: [
          ListView.builder(
            padding: const EdgeInsets.only(top: 20),
            itemCount: liveStreamList.length,
            itemBuilder: (context, index) {
              return (liveStreamList.length - 1) == index
                  ? VisibilityDetector(
                      key: Key(index.toString()),
                      onVisibilityChanged: (VisibilityInfo info) {
                        if (!stop && (index == liveStreamList.length - 1)) {
                          getStreamList(isFirst: false);
                        }
                      },
                      child: Column(
                        children: [
                          liveStreamWidget(index),
                          ValueListenableBuilder<bool>(
                            valueListenable: isPageLoading,
                            builder: (context, value, _) {
                              if (isPageLoading.value) {
                                return Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Center(
                                      child: Image.asset(
                                        loadingNewCompressGif,
                                        fit: BoxFit.fill,
                                        height: 45.h,
                                        width: 45.w,
                                      ),
                                    ),
                                  ],
                                );
                              }
                              return const SizedBox();
                            },
                          )
                        ],
                      ),
                    )
                  : liveStreamWidget(index);
            },
          ),
          if (!isLoading.value && liveStreamList.isEmpty)
            Center(
              child: Text(
                'NO DATA FOUND',
                style: TextStyle(
                  fontFamily: 'Nexa-Font',
                  color: AppColors.primaryColor,
                  fontSize: 18,
                ),
              ),
            ),
          ValueListenableBuilder<bool>(
            valueListenable: isLoading,
            builder: (context, value, child) {
              if (value) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Center(
                      child: Image.asset(
                        loadingNewCompressGif,
                        fit: BoxFit.fill,
                        height: 45.h,
                        width: 45.w,
                      ),
                    ),
                  ],
                );
              } else {
                return SizedBox();
              }
            },
          )
        ]));
  }

  Widget liveStreamWidget(index) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => BuyStreamPage(
              selfMade: true,
            ),
          ),
        );
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: ProductWidget(
            liveStreamModel: liveStreamList[index],
            index: index,
            viewOnPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => LiveStreamPage(),
                ),
              );
            },
            buyOnPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => BuyStreamPage(),
                ),
              );
            }),
      ),
    );
  }
}
