import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:groceryboouser/Party_Phase/utility/utility.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_colors.dart';
import 'package:groceryboouser/Styles/my_icons.dart';
import 'package:groceryboouser/Widgets/common_button_widget.dart';

class PaymentSuccessPage extends StatefulWidget {
  const PaymentSuccessPage({super.key});

  @override
  State<PaymentSuccessPage> createState() => _PaymentSuccessPageState();
}

class _PaymentSuccessPageState extends State<PaymentSuccessPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Align(
            alignment: Alignment.center,
            child: Column(
              children: [
                SizedBox(
                  height: 100.h,
                  width: 104.w,
                  child: UtilityForParty.imageLoader(
                    url: grey_bg,
                    placeholder: grey_bg,
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
                SizedBox(
                  height: 20.h,
                ),
                Text(
                  'Payment Successful',
                  style: AppTextStyle.xLargeBold,
                ),
                SizedBox(
                  height: 8.h,
                ),
                Text(
                  'Paid : \$1000',
                  style: AppTextStyle.mediumRegular.copyWith(color: light_black_333333),
                ),
                SizedBox(
                  height: 8.h,
                ),
                Text(
                  'Trxn ID : 12121212',
                  style: AppTextStyle.mediumRegular.copyWith(color: light_black_333333),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 8.h,
          ),
          Divider(
            color: AppColors.textMuted,
          ),
          SizedBox(
            height: 8.h,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Product Name',
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyle.mediumRegular.copyWith(fontWeight: FontWeight.w600),
                ),
                SizedBox(
                  height: 4.h,
                ),
                Text(
                  'Product Description' * 50,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyle.mediumRegular.copyWith(color: light_black_333333),
                ),
                SizedBox(
                  height: 4.h,
                ),
                Text(
                  '12 July 2025',
                  style: AppTextStyle.mediumRegular.copyWith(color: light_black_333333),
                ),
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
            child: CommonButtonWidget(
              onTap: () {},
              title: 'View',
              buttonColor: pink_D13784,
            ),
          ),
        ],
      ),
    );
  }
}
