// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:groceryboouser/Party_Phase/Create_Party/widget/app_checkbox.dart';
import 'package:groceryboouser/Party_Phase/utility/utility.dart';
import 'package:groceryboouser/Screens/OurServices/Categoryscreen/model/ProductModel.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_icons.dart';
import 'package:responsive_builder/responsive_builder.dart';

class ProductsWidget extends StatelessWidget {
  const ProductsWidget({
    super.key,
    this.storeProduct,
    this.isSelected = false,
    this.onTap,
    this.onlyForDisplay = false,
    this.onTapDetail,
  });

  final Product? storeProduct;
  final VoidCallback? onTap;
  final VoidCallback? onTapDetail;
  final bool isSelected;
  final bool onlyForDisplay;

  @override
  Widget build(BuildContext context) {
    final isTablet = getValueForScreenType<bool>(
      context: context,
      mobile: false,
      tablet: true,
    );
    return InkWell(
      onTap: onTapDetail,
      borderRadius: BorderRadius.circular(8),
      overlayColor: WidgetStateProperty.all(Colors.grey.withAlpha(20)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              height: 75,
              width: 75,
              child: UtilityForParty.imageLoader(
                  url: storeProduct?.image ?? '',
                  placeholder: place_holder_png,
                  borderRadius: BorderRadius.circular(8)),
            ),
            const SizedBox(
              width: 10,
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          "Product Name",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: AppTextStyle.mediumRegular.copyWith(
                            fontSize: 18.sp,
                          ),
                        ),
                      ),
                      const SizedBox(
                        width: 8,
                      ),
                      if (!onlyForDisplay)
                        InkWell(
                          onTap: onTap,
                          child: AppCheckbox(
                            value: isSelected,
                            fontSize: 18,
                            size: 19,
                          ),
                        ),
                    ],
                  ),
                  SizedBox(
                    height: 2.h,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(right: 30.0),
                    child: Text(
                      "\$12",
                      // 'Nigeria America Ghana',
                      style: AppTextStyle.mediumRegular.copyWith(
                        fontSize: 14.sp,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                  SizedBox(
                    height: 2.h,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(right: 30.0),
                    child: Text(
                      "Product description",
                      // 'Nigeria America Ghana',
                      style: AppTextStyle.mediumRegular.copyWith(
                        fontSize: 14.sp,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
