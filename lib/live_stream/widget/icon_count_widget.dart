import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';

class IconCountWidget extends StatelessWidget {
  const IconCountWidget({super.key, this.icon, this.count, this.onTap});
  final IconData? icon;
  final int? count;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 50.h,
        width: 50.w,
        child: Column(
          children: [
            Icon(
              icon,
              color: AppColors.white,
              size: 30,
            ),
            Text(
              count.toString(),
              style: AppTextStyle.smallMedium.copyWith(
                color: AppColors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
