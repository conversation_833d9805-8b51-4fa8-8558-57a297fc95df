import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_colors.dart';
import 'package:groceryboouser/Styles/my_icons.dart';
import 'package:groceryboouser/live_stream/model/live_stream_list_model.dart';
import 'package:intl/intl.dart';

class ProductWidget extends StatelessWidget {
  const ProductWidget(
      {super.key, required this.index, this.viewOnPressed, this.buyOnPressed, required this.liveStreamModel});
  final int index;
  final VoidCallback? viewOnPressed;
  final VoidCallback? buyOnPressed;
  final LiveStreamModel liveStreamModel;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: CachedNetworkImage(
            key: Un<PERSON><PERSON><PERSON>(),
            width: 60.w,
            height: 60.h,
            imageUrl: '',
            fit: BoxFit.cover,
            placeholder: (context, url) => Image.asset(
              grey_bg,
              width: 60.w,
              height: 60.h,
              fit: BoxFit.fill,
            ),
            errorWidget: (context, url, error) => Image.asset(
              grey_bg,
              width: 60.w,
              height: 60.h,
              fit: BoxFit.fill,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Flexible(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(
                    child: Text(
                      liveStreamModel.title ?? '',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: AppTextStyle.smallMedium.copyWith(
                        color: AppColors.blackColor,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                  Text(
                    liveStreamModel.scheduledAt != null
                        ? DateFormat('dd-MM-yyyy').format(DateTime.tryParse(liveStreamModel.scheduledAt!)!)
                        : '',
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: AppTextStyle.smallRegular.copyWith(color: AppColors.textMuted),
                  ),
                ],
              ),
              Row(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        liveStreamModel.description ?? '',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: AppTextStyle.smallRegular.copyWith(color: AppColors.textMuted),
                      ),
                      Row(
                        children: [
                          Text("${"John Doe"}",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: AppTextStyle.smallRegular.copyWith(color: AppColors.textMuted)),
                          // Text("       \$20.00",
                          //     maxLines: 1,
                          //     overflow: TextOverflow.ellipsis,
                          //     style: AppTextStyle.smallRegular.copyWith(color: AppColors.textMuted)),
                        ],
                      ),
                    ],
                  ),
                  const Spacer(),
                  index == 0
                      ? ElevatedButton(
                          onPressed: viewOnPressed,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: pink_d03784,
                          ),
                          child: Text("View",
                              style:
                                  AppTextStyle.smallMedium.copyWith(color: index == 0 ? AppColors.white : pink_d03784)),
                        )
                      : OutlinedButton(
                          onPressed: buyOnPressed,
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(
                              color: pink_d03784,
                            ),
                          ),
                          child: Text("\$20.00", style: AppTextStyle.smallMedium.copyWith(color: pink_d03784)),
                        ),
                ],
              ),
            ],
          ),
        ),
        // const SizedBox(width: 12),
      ],
    );
  }
}
