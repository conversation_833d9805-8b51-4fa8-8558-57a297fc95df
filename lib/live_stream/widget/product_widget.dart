import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_icons.dart';

class ProductWidget extends StatelessWidget {
  const ProductWidget({super.key, required this.index, this.viewOnPressed, this.buyOnPressed});
  final int index;
  final VoidCallback? viewOnPressed;
  final VoidCallback? buyOnPressed;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: CachedNetworkImage(
            key: UniqueKey(),
            width: 60.w,
            height: 60.h,
            imageUrl: '',
            fit: BoxFit.cover,
            placeholder: (context, url) => Image.asset(
              grey_bg,
              width: 60.w,
              height: 60.h,
              fit: BoxFit.fill,
            ),
            errorWidget: (context, url, error) => Image.asset(
              grey_bg,
              width: 60.w,
              height: 60.h,
              fit: BoxFit.fill,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Food",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: AppTextStyle.smallMedium.copyWith(
                  color: AppColors.blackColor,
                  fontWeight: FontWeight.w700,
                ),
              ),
              Text(
                "21 Feb 2025",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: AppTextStyle.smallRegular.copyWith(color: AppColors.textMuted),
              ),
              Text("\$400",
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyle.smallRegular.copyWith(color: AppColors.textMuted)),
            ],
          ),
        ),
        const SizedBox(width: 12),
        index == 0
            ? ElevatedButton(
                onPressed: viewOnPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor,
                ),
                child: Text("View",
                    style: AppTextStyle.smallMedium
                        .copyWith(color: index == 0 ? AppColors.white : AppColors.primaryColor)),
              )
            : OutlinedButton(
                onPressed: buyOnPressed,
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(
                    color: AppColors.primaryColor,
                  ),
                ),
                child: Text("Buy", style: AppTextStyle.smallMedium.copyWith(color: AppColors.primaryColor)),
              ),
      ],
    );
  }
}
