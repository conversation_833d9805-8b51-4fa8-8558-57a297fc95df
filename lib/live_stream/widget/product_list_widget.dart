import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:groceryboouser/Party_Phase/utility/utility.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_icons.dart';

class ProductListWidget extends StatelessWidget {
  const ProductListWidget({super.key, required this.onTap});
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.only(right: 8.w),
        width: 104.w,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              height: 100.h,
              width: 104.w,
              child: UtilityForParty.imageLoader(
                url: '',
                placeholder: place_holder_png,
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            SizedBox(
              height: 8.h,
            ),
            Text(
              'Product Name',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: AppTextStyle.smallMedium.copyWith(
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
