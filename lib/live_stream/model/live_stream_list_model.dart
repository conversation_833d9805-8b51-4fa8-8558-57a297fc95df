class LiveStreamListResponse {
  bool? success;
  int? statusCode;
  String? message;
  bool? showToast;
  List<LiveStreamModel>? data;

  LiveStreamListResponse({this.success, this.statusCode, this.message, this.showToast, this.data});

  LiveStreamListResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    statusCode = json['status_code'];
    message = json['message'];
    showToast = json['show_toast'];
    if (json['data'] != null) {
      data = <LiveStreamModel>[];
      json['data'].forEach((v) {
        data!.add(new LiveStreamModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['status_code'] = this.statusCode;
    data['message'] = this.message;
    data['show_toast'] = this.showToast;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class LiveStreamModel {
  int? id;
  int? userId;
  String? title;
  String? description;
  String? type;
  Null thumbnail;
  Null video;
  String? category;
  String? scheduledAt;
  int? price;
  String? createdAt;
  String? updatedAt;
  Null deletedAt;
  List<Products>? products;

  LiveStreamModel(
      {this.id,
      this.userId,
      this.title,
      this.description,
      this.type,
      this.thumbnail,
      this.video,
      this.category,
      this.scheduledAt,
      this.price,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.products});

  LiveStreamModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    title = json['title'];
    description = json['description'];
    type = json['type'];
    thumbnail = json['thumbnail'];
    video = json['video'];
    category = json['category'];
    scheduledAt = json['scheduled_at'];
    price = json['price'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    deletedAt = json['deleted_at'];
    if (json['products'] != null) {
      products = <Products>[];
      json['products'].forEach((v) {
        products!.add(new Products.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['user_id'] = this.userId;
    data['title'] = this.title;
    data['description'] = this.description;
    data['type'] = this.type;
    data['thumbnail'] = this.thumbnail;
    data['video'] = this.video;
    data['category'] = this.category;
    data['scheduled_at'] = this.scheduledAt;
    data['price'] = this.price;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['deleted_at'] = this.deletedAt;
    if (this.products != null) {
      data['products'] = this.products!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Products {
  int? id;
  int? userId;
  int? storeId;
  int? categoryId;
  Null subCategoryId;
  Null leafCategoryId;
  Null productId;
  Null masterProductId;
  Null superMasterProductId;
  Null isImported;
  String? name;
  String? description;
  int? isVarients;
  String? tags;
  int? isForPromo;
  String? image;
  Null video;
  int? status;
  String? cost;
  Null weightUnit;
  String? usdCost;
  Null replacementProduct;
  int? isRecommonded;
  int? isOverseas;
  int? isMasterProduct;
  int? isAttribute;
  int? stock;
  int? masterProduct;
  String? createdAt;
  String? updatedAt;
  Null deletedAt;
  String? productType;
  Null createdBy;
  Null referenceProductId;
  Null size;
  int? isMaster;
  String? isStoreOpen;
  Null userBasketItemCount;
  Null featuredBasketId;
  int? basketProductValue;
  String? regularPrice;
  Pivot? pivot;
  Store? store;

  Products(
      {this.id,
      this.userId,
      this.storeId,
      this.categoryId,
      this.subCategoryId,
      this.leafCategoryId,
      this.productId,
      this.masterProductId,
      this.superMasterProductId,
      this.isImported,
      this.name,
      this.description,
      this.isVarients,
      this.tags,
      this.isForPromo,
      this.image,
      this.video,
      this.status,
      this.cost,
      this.weightUnit,
      this.usdCost,
      this.replacementProduct,
      this.isRecommonded,
      this.isOverseas,
      this.isMasterProduct,
      this.isAttribute,
      this.stock,
      this.masterProduct,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.productType,
      this.createdBy,
      this.referenceProductId,
      this.size,
      this.isMaster,
      this.isStoreOpen,
      this.userBasketItemCount,
      this.featuredBasketId,
      this.basketProductValue,
      this.regularPrice,
      this.pivot,
      this.store});

  Products.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    storeId = json['store_id'];
    categoryId = json['category_id'];
    subCategoryId = json['sub_category_id'];
    leafCategoryId = json['leaf_category_id'];
    productId = json['product_id'];
    masterProductId = json['master_product_id'];
    superMasterProductId = json['super_master_product_id'];
    isImported = json['is_imported'];
    name = json['name'];
    description = json['description'];
    isVarients = json['is_varients'];
    tags = json['tags'];
    isForPromo = json['is_for_promo'];
    image = json['image'];
    video = json['video'];
    status = json['status'];
    cost = json['cost'];
    weightUnit = json['weight_unit'];
    usdCost = json['usd_cost'];
    replacementProduct = json['replacement_product'];
    isRecommonded = json['is_recommonded'];
    isOverseas = json['is_overseas'];
    isMasterProduct = json['is_master_product'];
    isAttribute = json['is_attribute'];
    stock = json['stock'];
    masterProduct = json['master_product'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    deletedAt = json['deleted_at'];
    productType = json['product_type'];
    createdBy = json['created_by'];
    referenceProductId = json['reference_product_id'];
    size = json['size'];
    isMaster = json['is_master'];
    isStoreOpen = json['is_store_open'];
    userBasketItemCount = json['user_basket_item_count'];
    featuredBasketId = json['featured_basket_id'];
    basketProductValue = json['basket_product_value'];
    regularPrice = json['regular_price'];
    pivot = json['pivot'] != null ? new Pivot.fromJson(json['pivot']) : null;
    store = json['store'] != null ? new Store.fromJson(json['store']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['user_id'] = this.userId;
    data['store_id'] = this.storeId;
    data['category_id'] = this.categoryId;
    data['sub_category_id'] = this.subCategoryId;
    data['leaf_category_id'] = this.leafCategoryId;
    data['product_id'] = this.productId;
    data['master_product_id'] = this.masterProductId;
    data['super_master_product_id'] = this.superMasterProductId;
    data['is_imported'] = this.isImported;
    data['name'] = this.name;
    data['description'] = this.description;
    data['is_varients'] = this.isVarients;
    data['tags'] = this.tags;
    data['is_for_promo'] = this.isForPromo;
    data['image'] = this.image;
    data['video'] = this.video;
    data['status'] = this.status;
    data['cost'] = this.cost;
    data['weight_unit'] = this.weightUnit;
    data['usd_cost'] = this.usdCost;
    data['replacement_product'] = this.replacementProduct;
    data['is_recommonded'] = this.isRecommonded;
    data['is_overseas'] = this.isOverseas;
    data['is_master_product'] = this.isMasterProduct;
    data['is_attribute'] = this.isAttribute;
    data['stock'] = this.stock;
    data['master_product'] = this.masterProduct;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['deleted_at'] = this.deletedAt;
    data['product_type'] = this.productType;
    data['created_by'] = this.createdBy;
    data['reference_product_id'] = this.referenceProductId;
    data['size'] = this.size;
    data['is_master'] = this.isMaster;
    data['is_store_open'] = this.isStoreOpen;
    data['user_basket_item_count'] = this.userBasketItemCount;
    data['featured_basket_id'] = this.featuredBasketId;
    data['basket_product_value'] = this.basketProductValue;
    data['regular_price'] = this.regularPrice;
    if (this.pivot != null) {
      data['pivot'] = this.pivot!.toJson();
    }
    if (this.store != null) {
      data['store'] = this.store!.toJson();
    }
    return data;
  }
}

class Pivot {
  int? streamId;
  int? productId;

  Pivot({this.streamId, this.productId});

  Pivot.fromJson(Map<String, dynamic> json) {
    streamId = json['stream_id'];
    productId = json['product_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['stream_id'] = this.streamId;
    data['product_id'] = this.productId;
    return data;
  }
}

class Store {
  int? id;
  int? userId;
  int? cityId;
  int? stateId;
  int? countryId;
  Null masterStoreId;
  String? name;
  String? email;
  String? contactNumber;
  String? address;
  String? latitude;
  String? longitude;
  Null businessType;
  int? isLive;
  String? blockNumber;
  String? street;
  String? landmark;
  String? pincode;
  String? commission;
  Null image;
  Null cardImage;
  Null banner;
  String? fileType;
  int? status;
  String? openTime;
  String? closeTime;
  String? localDeliveryCharge;
  String? internationalDeliveryCharge;
  Null storeTiming;
  int? isRecommendedMarket;
  int? isUsePromo;
  String? createdAt;
  String? updatedAt;
  Null deletedAt;

  Store(
      {this.id,
      this.userId,
      this.cityId,
      this.stateId,
      this.countryId,
      this.masterStoreId,
      this.name,
      this.email,
      this.contactNumber,
      this.address,
      this.latitude,
      this.longitude,
      this.businessType,
      this.isLive,
      this.blockNumber,
      this.street,
      this.landmark,
      this.pincode,
      this.commission,
      this.image,
      this.cardImage,
      this.banner,
      this.fileType,
      this.status,
      this.openTime,
      this.closeTime,
      this.localDeliveryCharge,
      this.internationalDeliveryCharge,
      this.storeTiming,
      this.isRecommendedMarket,
      this.isUsePromo,
      this.createdAt,
      this.updatedAt,
      this.deletedAt});

  Store.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    cityId = json['city_id'];
    stateId = json['state_id'];
    countryId = json['country_id'];
    masterStoreId = json['master_store_id'];
    name = json['name'];
    email = json['email'];
    contactNumber = json['contact_number'];
    address = json['address'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    businessType = json['business_type'];
    isLive = json['is_live'];
    blockNumber = json['block_number'];
    street = json['street'];
    landmark = json['landmark'];
    pincode = json['pincode'];
    commission = json['commission'];
    image = json['image'];
    cardImage = json['card_image'];
    banner = json['banner'];
    fileType = json['file_type'];
    status = json['status'];
    openTime = json['open_time'];
    closeTime = json['close_time'];
    localDeliveryCharge = json['local_delivery_charge'];
    internationalDeliveryCharge = json['international_delivery_charge'];
    storeTiming = json['store_timing'];
    isRecommendedMarket = json['is_recommended_market'];
    isUsePromo = json['is_use_promo'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    deletedAt = json['deleted_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['user_id'] = this.userId;
    data['city_id'] = this.cityId;
    data['state_id'] = this.stateId;
    data['country_id'] = this.countryId;
    data['master_store_id'] = this.masterStoreId;
    data['name'] = this.name;
    data['email'] = this.email;
    data['contact_number'] = this.contactNumber;
    data['address'] = this.address;
    data['latitude'] = this.latitude;
    data['longitude'] = this.longitude;
    data['business_type'] = this.businessType;
    data['is_live'] = this.isLive;
    data['block_number'] = this.blockNumber;
    data['street'] = this.street;
    data['landmark'] = this.landmark;
    data['pincode'] = this.pincode;
    data['commission'] = this.commission;
    data['image'] = this.image;
    data['card_image'] = this.cardImage;
    data['banner'] = this.banner;
    data['file_type'] = this.fileType;
    data['status'] = this.status;
    data['open_time'] = this.openTime;
    data['close_time'] = this.closeTime;
    data['local_delivery_charge'] = this.localDeliveryCharge;
    data['international_delivery_charge'] = this.internationalDeliveryCharge;
    data['store_timing'] = this.storeTiming;
    data['is_recommended_market'] = this.isRecommendedMarket;
    data['is_use_promo'] = this.isUsePromo;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['deleted_at'] = this.deletedAt;
    return data;
  }
}
