import 'package:groceryboouser/live_stream/model/live_stream_list_model.dart';

class CreateLiveStreamResponse {
  bool? success;
  int? statusCode;
  String? message;
  bool? showToast;
  LiveStreamModel? data;

  CreateLiveStreamResponse({this.success, this.statusCode, this.message, this.showToast, this.data});

  CreateLiveStreamResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    statusCode = json['status_code'];
    message = json['message'];
    showToast = json['show_toast'];
    data = json['data'] != null ? new LiveStreamModel.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['status_code'] = this.statusCode;
    data['message'] = this.message;
    data['show_toast'] = this.showToast;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}
