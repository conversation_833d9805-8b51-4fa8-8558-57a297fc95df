import 'package:groceryboouser/Utils/env_config.dart';

// const urlBase = 'https://lovgrub.store/test/public/api'; //Base Url (26-06-23)

// const urlBase =
//     kDebugMode ? "https://lovgrub.store/test/public/api" : 'https://lovgrub.store/api'; //live url (21-06-2024)

// const urlBase = 'https://lovgrub.store/test/public/api'; // test url
// const urlBase = 'https://lovgrub.store/api'; // live
// const urlBase = 'http://35.200.138.47/api'; // live

const urlBase = EnvConfig.baseURL; // NEW

//Authentication
const urlGetLogin = '/common/mobile/login';
const urlCheckEmailEntryInDB = '/common/check-email';
const urlPostRegister = '/common/mobile/register';
const urlPostRegisterStep1 = '/common/mobile/register/step-first';
const urlPostEmailVerification = '/common/mobile/email/varification';
const urlPostRegisterStep2 = '/common/mobile/register/step-two';
const urlPostPhoneVerification = '/common/mobile/phone/varification';
const urlPostResendCode = '/common/mobile/email/resend-code';
const urlPostResendOtp = '/common/mobile/phone/resend-otp';
const urlPostSocialLogin = '/common/mobile/social-login';
const urlSocialRegisterLogin = '/common/mobile/register/social-login';
const urlSyncContact = '/user/sync-contact';

/// secret key api
// const urlSecretKey = '/user/secret-key-create';

/// home
const urlHomeStoreAd = '/user/store-adv-details';
// const urlStoreAdvertisement = '/user/advertisement/ad_display';
const urlStoreAdvertisement = '/user/store/subcategory-list-ad';
//Stripe APIs (Third party API)
// 1. To generate payment token by adding card details
// https://api.stripe.com/v1/tokens

//API call when user complete all authentication steps
const urlAuthenticationComplete = '/common/authenticate';
// const urlStoreWiseProduct = '/user/product/search-speed';
const urlStoreWiseProduct = '/user/product/search';

//Country-State-City list
const urlPostCountryList = '/common/country-list';
const urlPostStateList = '/common/state-list';
const urlPostCityList = '/common/city-list';

//Forgot password flow APIs
const urlForgotPassword = '/common/mobile/forgot-password';
const urlVerifiedOtp = '/common/mobile/verify-otp';
const urlResendOtp = '/common/mobile/resend-otp-mail';
const urlResetPassword = '/common/mobile/reset-password';

//Address APIs
const urlAddressCreate = '/user/address/create';
const urlAddressesList = '/user/address/list';
const urlAddressDelete = '/user/address/delete';
const urlAddressEdit = '/user/address/edit';
const urlAddcressCreateParty = '/restaurant/party_address/create';
//This API is called when token got expired
///not integrate
// const urlAutoLogin = '/common/mobile/auto-login';

// Profile Edit APIs
const urlPostEditProfile = '/user/edit-profile';
const urlPostEditUserProfile = '/user/edit-profile';
const urlPostAcountDelete = '/common/delete-account';
const urlPostChangePassword = '/user/change-password';

//Follow & unfollow API
const urlPostFollowingList = '/user/following-list';
const urlPostFollowerList = '/user/follower-list';
const urlPostFollowUser = '/user/follow-user';
const urlPostUnfollowUser = '/user/unfollow-user';

// Display User profile APIs
const urlPostUserProfile = '/user/user-profile';
const urlPostUserProfilePins = '/user/user-profile-pins';

//Pin details APIs
const urlPostPinnedDetails = '/user/pinned/details';
const urlPostTotalPurchasedPins = '/user/purchased-pins';
const urlPostRepinnedBy = '/user/repinned-by';

//Other User Profle APIs
const urlPostGetOtherUserProfile = '/user/get-userbyid'; //to get other user's profile
const urlPostGetOtherUserProfilePins = '/user/get-userbyid-pins'; //to get other user's profile
const urlPostReportReasonsList = '/user/report-list'; //to get other user's profile
const urlPostReportUser = '/user/user-report';
const urlPinPostReportUser = '/user/user-pin-report';

//Display banners in user profile
const urlPostCoverImageListForPersonalProfile = '/user/coverimage/list'; //to get other user's profile
const urlPostCoverImageListForOtherUserProfile = '/user/get-userbyid/coverimage/list'; //to get other user's profile
const urlPostUploadBannerInUserProfile = '/user/banner'; //to get other user's profile

//Logout APIs
const urlLogout = '/common/logout';

//Boost APIs
const urlPostCreateBoost = '/user/boost/create';
const urlPostTargetCount = '/user/targeted/count'; // To get available users list within that area
const urlPostBoostDetails = '/user/boost/detail';
const urlPostBoostList = '/user/boost/list';
const urlPostPurchasePin = '/user/purchase-pin';
const urlPostStopBoost = '/user/boost/stop';
const urlPostBoostUserWalletBalance = '/user/boost/wallet-balance'; //to get default wallet balance
const urlPostBoostCheckPayment = '/user/boost/check-payment';
const urlPostBoostPaymentTransaction = '/user/boost/payment-transaction';
const urlPostBoostTransactionStatus = '/user/boost/transaction-status';
const urlPostBoostEmptyPaystackAmount = '/user/boost/empty-paystack-amount';
const urlPostBoostPerDayCost = '/user/boost/perday-cost';
const urlPostBoostNoOfClicks = '/user/boost/no-of-clicks';
// const urlPostBoostTransactionStatus = '/user/boost/transaction-status';

// Advertisement APIs
const urlCreateAdvertisement = '/user/advertisement/create';
const urlAdvertisementDetails = '/user/advertisement/detail';
const urlAdvertisementUploadBanner = '/user/advertisement/upload/banner';
const urlAdvertisementList = '/user/advertisement/list';
const urlAdvertisementWalletBalance = '/user/advertisement/wallet-balance';
const urlAdvertisementCheckPayment = '/user/advertisement/check-payment';
const urlAdvertisementPaymentTransaction = '/user/advertisement/payment-transaction';
const urlAdvertisementTransactionStatus = '/user/advertisement/transaction-status';
const urlAdvertisementCostPerday = '/user/advertisement/perday-cost';
const urlAdvertisementEmptyPayStackAmount = '/user/advertisement/empty-paystack-amount';
const urlAdvertisementNoOfClicks = '/user/advertisement/no-of-clicks';

//Notification APIs

const urlNotificationList = '/user/notification/list';
const urlNotificationRead = '/user/notification/read';
const urlNotificationUnreadCount = '/user/notification/unread-count';
const urlNotificationClearAll = '/user/notification/clear-all';

//Category APIs
const urlcategoryList = '/user/category/list';

//New Category List API
const urlNewCategoryList = '/user/whitelable-category-list';

//Banner APIs
const urlBannerList = '/user/banner/home';

//ProductList APIs
const urlproductList = '/user/product/list';
urlproductListIncart(int id) => '/user/store/$id/store-wise-cart';

//Add To Cart APIs
const urladdToCart = '/user/basket/add-item';
const urladdToCartMultiple = '/user/basket/add-item-multiple';

//ProductList APIs
const urlSingleproduct = '/user/product/details';

//Collection APIs
const userCollectionList = '/user/usercollection/list';
const createCollection = '/user/usercollection/create';

const createCollectionCopy = '/user/usercollection/create';

//BasketList APIs
// const urlBasketList = '/user/basket/item-list';
const urlBasketList = '/user/basket/checkout-info';
const urlNewBasketList = '/user/basket/item-list-speed';
// use for display item count and toata
// const urlCartItems = '/user/basket/item-list-simple-speed';
const urlCartItems = '/user/basket/item-list-simple';
const urlServiceBoyList = '/common/service_boy_list';

//RemoveBasketItem APIs
const urlRemovebasketItem = '/user/basket/remove-item';

//OnGoingOrder APIs
const urlOngoingOrders = '/user/ongoing/orders';

//pastOder APIs
const urlPastOrders = '/user/past/orders';

//It gives total order count of ongoing & past orders
const urlTotalOrderCount = '/user/order/count';

//orderDetails APIs
const urlOrderDetails = '/user/order/details';

//Repeate OrderAPI
const urlrepeateOrder = '/user/order/repeat-order';

//Pinned APIs
const pinnedList = '/user/pinned/list';

//orderDetails APIs
///not integrate
// const urladdressList = '/user/address/list';

//wallet APIs
const urlWalletDetails = '/user/transaction/history';

// Pin Created  API
const urlPinnedCreated = '/user/pinned/create';

// Edit Pin Details
const urlEditPinnedDetails = '/user/pinned/edit';

// OrderPlaced API
// const urlOrderPlaced = '/user/order/place';
const urlOrderPlaced = '/user/order/place-speed';

// RecommendedPinnedList API
const urlRecommendedPinnedList = '/user/pinned/popular-pin';
// const urlRecommendedPinnedList = '/user/pinned/list';

// RecommendedPinnedList API

//withdraw APIs
const urlRecentWithdraw = '/user/withdraw/recent-list';
const urlAddAccount = '/user/bank-details/create';
const urlAccountList = '/user/bank-details/list';
const urlSendWithdrawRequest = '/user/withdraw/send-request';

// top up apis
const topUpWallet = '/user/transaction/add-topup-wallet';

const urlUpdateCartDetails = '/user/basket/update-product-value';

// Pinned Revenue
const urlPinnedCommission = '/user/pinned/comission';
const urlPinDetails = '/user/pinned/revenue';

// RecommendedPinnedList API
// const urlFollowboos= '/user/other-boos';
const urlFollowBoosApi = '/user/follow-boos';

//RecentSearch add  API
const urlRecentSearch = '/common/mobile/recent-search/create';

// PopularBasket List  API
const urlPopularbasket = '/user/pinned/popular-pin';

// RecommendedPinnedList API
const urlRecentOrder = '/user/order/recent';

// urlRecentSearchList API
const urlRecentSearchList = '/common/mobile/recent-search/list';

// RecentSearchList Delete API
const urlRecentSearchDeleteAll = '/common/mobile/recent-search/clear';

// PopularBasket List  API
const urlDeleteSingleRecentSearch = '/common/mobile/recent-search/delete';

// ProductSearch Result List  API
const urlProductSearchResult = '/common/search/product';

// BasketSearch Result List  API
const urlBasketSearchResult = '/common/search/basket';

// userSearch Result List  API
const urlUserSearchResult = '/common/search/user';

// Feed Apis
const userPinsList = '/user/pins-of-user';
const addToCollection = '/user/usercollection/add-item';
const collectionDetailsList = '/user/usercollection/details';
const removeFromCollectionApi = '/user/usercollection/remove-item';
const addPinOtherUser = '/user/addpin-otheruser';
const urlRemovePin = '/user/pinned/remove-pin';
const feedList = '/user/feed/feed-list';
const otherBoos = '/user/other-boos';
const feedBanner = '/user/banner/feed';
const urlNoOfClicks = '/user/advertisement/no-of-clicks';

//Replace product
const urlPinAlreadyInBasket = '/user/check-alreadyIn-basket';
const urlPostOwnPins = '/user/own-pins';
const productDetailApi = '/user/product/details';
const replaceProductApi = '/user/replace-product/list';
const deleteProductApi = '/user/pinned/item/delete';
const replaceApi = '/user/replace-product';

///not integrate
const replacedProductListApi = '/user/after-product-replace-list';
const removeOutOfStockItemApi = '/user/product/out-of-stock';

// Chat Api
const urlMessageByUserList = '/user/message-by-user-list';

//Ad Revenue
const urlAdsRevenueApi = '/user/advertisement/revenue';

//Market URLS
const urlStoreList = '/user/store/list';
const urlLiveStoreList = '/user/store/live-stores';
const urlStoreDetailsList = '/user/store/details';
const urlRecommendedPinned = '/user/store/recommended-pin-list';
const urlMarketSearchList = '/user/store/search-product';
const cityNameUrl = '/common/city-name-by-id';
//Recurring Order
const urlRecurringOrderList = '/user/order/recurring/list';
const urlRecurringOrderDetails = '/user/order/recurring/details';
const urlCancelOrder = '/user/order/recurring/order-cancel';
const urlCancelSubscription = '/user/order/recurring/subscription-cancel';
const urlReorderList = '/user/order/repeat-order';

///not integrate

const urlStoreWiseCategories = '/user/store/category-list';
const urlStoreWiseCategoriesAndProducts = '/user/store/category-products';

const urlCheckProduct = '/user/check-product';
const urlReplaceProduct = '/user/replace-basket-products';

const urlSubcategoryProductlistHome = '/user/product/sub-category';

const urlStoreDetails = '/user/store/details';
const urlSubcategorylist = '/user/store/subcategory-list';
const urlProductlist = '/user/store/category-products';
const urlHomeAllProductList = "/user/product/category";
const urlStoreChildSubCateProductlist = '/user/store/childcategory-products';
const urlStoreSubProductDatalist = '/user/store/subcategory-products';
const commonProduct = '/user/product/common';

//PinDetails Screen in Remove Overseas 0 Product
///not integrate

const urlRemoveWithoutOverseas = "/user/product/check-overseas-product";
const urlChildCategoryWiseProduct = '/user/product/child-category';

//E-Commerece URL
const urlECommerceCategoryDetails = "/user/product/ecommerce-details";

const urlBlockUser = '/user/block-user';

//
const recommendStore = '/common/search/store';

//add recommended store
const urlRecommendedSupermarket = '/user/recomend-supermarket';
const urlAddSuperMarketAddress = '/user/add-supermarket-address';

const urlNotifications = "/common/notification";
const urlCheckVersionCode = "/user/app_settings";

// stripe payment gateway
const urlStripePayment = "https://api.stripe.com/v1/payment_intents";

// Restaurant module
const urlRestaurantHome = "/restaurant/home";
const urlEventBanner = "/restaurant/events/home";
const urlRestaurantList = "/restaurant/restaurantlist";
const urldefaultAddress = "/restaurant/address/default"; // to select default address in database
const urlRestaurantDetails = "/restaurant/restaurant_details";
const urlRestaurantProductList = "/restaurant/product/list";
const urlRestaurantVarientList = "/restaurant/product/varients";
const urlRestaurantAddProductsInBasket = "/restaurant/product/addProductInBasket";
const urlRestaurantCartDetails = "/restaurant/product/cart_details";
const urlRestaurantOrderDetails = "/restaurant/order/details";
const urlRestaurantClearCart = "/restaurant/product/clearcart";
const urlRestaurantViewBasket = "/restaurant/product/viewbasket";
const urlRestaurantDataBasket = "/restaurant/product/databasket";
const urlRestaurantUpdateProductBasket = "/restaurant/product/updateProductBasket";
const urlRestaurantRepeatOrder = "/restaurant/order/repeat_order";
const urlRestaurantPlaceOrder = "/restaurant/product/place_order";
const urlRestaurantOrderList = "/restaurant/order/list";
const urlEventUserList = "/restaurant/events/users";
const urlPartyBlockUser = "/user/block";

// caters module
const urlCatersHome = "/caters/home";
const urlSearchCaters = "/caters/caters_home";
const urlCatersVisit = "/caters/caters_details";
const urlMenuDetails = "/caters/event_details";
const urlCatersList = "/caters/my_orders";
const urlCatersOrderDetails = "/caters/my_order_details";
const urlCatersBookEvent = "/caters/book_now_event";
const urlCatersBookFamily = "/caters/book_now_family_event";
const urlCatersDeliveryCharge = "/caters/get_delivery_charge";
const urlGetCatersServiceFee = "/caters/get_service_fee";
const urlGetCatersMenuDetail = "/caters/product/details";
const urlAddCatersMenuItemInCart = "/caters/add_cater_to_cart";
const urlDeleteFromCart = "/caters/delete_from_cart";
const urlCaterCartDetails = "/caters/cater_cart_details";
const urlCaterCart = "/caters/cater_cart";
const urlEditCaterCart = "/caters/edit_cater_cart";
const urlCatersChatUserList = "/caters/chats";
const urlAddCatersMenuItemInCartV2 = "/caters/add_cater_to_cart_v2";
const urlCatersClearCart = "/caters/cater_clear_cart";

const addMenuCart = "/caters/add_menu_cart";
const editMenuCart = "/caters/edit_menu_cart";
const urlEventCartList = "/caters/list_menu_cart";
const urlAddOnsList = "/caters/get_items";
const urlAddAddOns = "/caters/add_item_cart";
const urlGetAddOns = "/caters/get_addons";
const urlClearMenuCart = "/caters/clear_menu_cart";

//New party
const getAddreshForParty = "/restaurant/party_address/list";
const deleteAddreshForParty = "/restaurant/party_address/delete";
const editAddreshForParty = "/restaurant/party_address/edit";
const getResturantForParty = "/restaurant/party_restaurant_list";
const createParty = "/restaurant/party/add";
const deleteParty = "/restaurant/party/delete";
const resturantDetail = "/restaurant/party/details";
const partyListAPI = "/restaurant/party/list";
const partyEditAPI = "/restaurant/party/edit";
const partyVisible = "/restaurant/party/visible";
const contactVisible = "/restaurant/party/visible_conatct_number";
const addressVisible = "/restaurant/party/visible_address";
const groupChatVisible = "/restaurant/party/allow-group-chat";

//Event
const eventListUrl = "/restaurant/events/list";
const eventCityListUrl = "/restaurant/events/city-list";
const eventDetailUrl = "/restaurant/events/details";
const eventResturantUrl = "/restaurant/events/restaurants";
const eventBannerUrl = "/restaurant/events/banners";

// Category list
const categoryListUrl = '/user/store_category/list';

const urlAddInBasketMultiple = "/restaurant/product/addUpdateProductInBasketMultiple";
const urlGroceryAddInBasketMultiple = "/user/basket/add-item-multiple-speed";

const createBankDerailsUrl = "/user/create/BankDetail";
const getBankDerailsUrl = "/user/bankDetailsList";
const getWalletUrl = "/user/wallet";
const withdrawBalanceUrl = "/user/withdraw";

const sendMessageApi = '$urlBase/caters/send-message';
const partSendMessageApi = '$urlBase/restaurant/party/send-notification';
const partySendMessageApi = '$urlBase/caters/send-party-message';
const eventSendMessageApi = '$urlBase/caters/send-event';

const urlGroceryChatUserList = "/user/grocery/chats";
const urlGroceryChatSendMessage = "$urlBase/user/grocery/send-message";

const urlStoreCategory = "$urlBase/user/categories";
const urlStoreFeatureBasket = "$urlBase/user/baskets/list";
const urlFeatureBasketList = "$urlBase/user/baskets";
const updateBasketCount = "$urlBase/user/baskets/update-count";
const groceryHomeApi = "$urlBase/user/store/home-screen";
urlFeatureBasketDetail(int basketId) => "$urlBase/user/baskets/showUserBasket/$basketId";
const userCardsApi = "$urlBase/user/cards";
const urlGroceryRandomStore = "$urlBase/user/store/rendom-stores";

// begin checkout
const beginCheckoutApi = "$urlBase/user/begin-checkout";
const deleteCardApi = "$urlBase/user/cards/delete";
const createSupport = "$urlBase/common/support/create";

// Grocery market tab new feature
const urlGroceryFavStore = "$urlBase/user/store/top-stores";
const urlGroceryStoreByCategory = "$urlBase/user/category/stores";

//promo feature
const urlPromoProductList = "$urlBase/user/promo-products";

const createLiveStreamApi = "$urlBase/user/streams/create";
const liveStreamListApi = "$urlBase/user/streams";
detailLiveStreamApi(int id) => "$urlBase/user/streams/$id/detail";
