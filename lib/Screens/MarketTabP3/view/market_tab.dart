import 'dart:convert';
import 'dart:developer';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:circular_profile_avatar/circular_profile_avatar.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:groceryboouser/Party_Phase/utility/utility.dart';
import 'package:groceryboouser/Screens/MarketTabP3/model/banner_response.dart';
import 'package:groceryboouser/Screens/MarketTabP3/model/order_late_notificaion_model.dart';
import 'package:groceryboouser/Screens/MarketTabP3/model/random_store_model.dart';
import 'package:groceryboouser/Screens/MarketTabP3/model/store_by_category_model.dart';
import 'package:groceryboouser/Screens/MarketTabP3/model/store_model.dart';
import 'package:groceryboouser/Screens/MarketTabP3/response/grocery_fav_store_response.dart';
import 'package:groceryboouser/Screens/MarketTabP3/response/random_store_response.dart';
import 'package:groceryboouser/Screens/MarketTabP3/response/store_by_category_response.dart';
import 'package:groceryboouser/Screens/MarketTabP3/view/category_search_screen.dart';
import 'package:groceryboouser/Screens/MarketTabP3/view/grocery_chats_list_page.dart';
import 'package:groceryboouser/Screens/MarketTabP3/view/search_recommend_store_screen_p3.dart';
import 'package:groceryboouser/Screens/MarketTabP3/view/store_detail_ui.dart';
import 'package:groceryboouser/Screens/MarketTabP3/widget/delivery_time_bottom_sheet.dart';
import 'package:groceryboouser/Screens/MarketTabP3/widget/live_store_item_view.dart';
import 'package:groceryboouser/Screens/MarketTabP3/widget/store_busy_sheet.dart';
import 'package:groceryboouser/Screens/MarketTabP3/widget/top_ten_sports_week_item_widget.dart';
import 'package:groceryboouser/Screens/Restaurant/view/restaurant_home_page_view_new.dart';
import 'package:groceryboouser/Screens/Restaurant/view/restaurant_video_ad_web_view_2.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Utils/preference_utils.dart';
import 'package:groceryboouser/Utils/snackbar.dart';
import 'package:groceryboouser/Utils/text_utilities.dart';
import 'package:groceryboouser/Widgets/app_asset_image.dart';
import 'package:groceryboouser/Widgets/custom_page_route.dart';
import 'package:groceryboouser/Widgets/custom_title_restaurant.dart';
import 'package:groceryboouser/Widgets/store_view_shimmer.dart';
import 'package:groceryboouser/chat/groceries_firebase_chat_service.dart';
import 'package:groceryboouser/live_stream/view/search_live_stream_page.dart';
import 'package:http/http.dart' as http;
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';

import '../../../Layouts/loading_screen.dart';
import '../../../Model/BaseModel.dart';
import '../../../Services/staticData.dart';
import '../../../Styles/app_text_style.dart';
import '../../../Styles/my_colors.dart';
import '../../../Styles/my_icons.dart';
import '../../../UiScreens/Caters_User_Phase/list_of_caters_details.dart';
import '../../../Utilities/Constant.dart';
import '../../../Utils/app_debouncer.dart';
import '../../../Utils/share_predata.dart';
import '../../../Welcome.dart';
import '../../../Widgets/common_shadow.dart';
import '../../../Widgets/store_card_view.dart';
import '../../Authentication/SignIn/model/SigninModel.dart';
import '../../Authentication/SignUp/model/CityListModel.dart';
import '../../Authentication/SignUp/model/StateListModel.dart';
import '../../HomePageP3/controller/home_controller.dart';
import '../../Location/controller/api_manager.dart';
import '../../MarketFlow/controller/MarketController.dart';
import '../../MarketFlow/model/MarketListModel.dart';
import '../../Networks/api_endpoint.dart';
import '../../Networks/api_response.dart';
import '../../Networks/session_out.dart';
import '../../Notification/view/NotificationListView.dart';
import '../../OurServices/Homepage/model/AddressModel.dart';
import '../../Restaurant/controller/restaurant_list_contoller.dart';
import '../model/grocery_home_model.dart';
import '../response/grocery_home_response.dart';
import 'StoreDetailP2.dart';

class MarketTab extends StatefulWidget {
  const MarketTab({
    Key? key,
    this.isFromDynamicLink = false,
    this.isFromOrderSummaryPage = false,
  }) : super(key: key);
  final bool isFromDynamicLink;

  final bool isFromOrderSummaryPage;

  @override
  State<MarketTab> createState() => _MarketTabState();
}

class _MarketTabState extends State<MarketTab> {
  MarketController marketController = Get.put(MarketController());
  HomePageController homeController = Get.put(HomePageController());
  RestaurantListController controller = Get.put(RestaurantListController());
  final _debouncer = AppDebouncer(milliseconds: 500);
  double appbarHeight = 420.0;
  ValueNotifier<bool> isBannerLoading = ValueNotifier<bool>(false);
  ValueNotifier<bool> allInOneLoading = ValueNotifier<bool>(false);
  BannerModel? eventsBanner;
  String? cityName;

  VideoPlayerController? videoController;
  AddressDatum? addressModelFORCITY;
  bool isMuted = true; //
  final loginModel = ValueNotifier<SigninModel?>(null);
  final isLoading = ValueNotifier<bool>(false);
  final isNewLoading = ValueNotifier<bool>(false);
  final isPageLoading = ValueNotifier<bool>(false);
  final isLivePageLoading = ValueNotifier<bool>(false);
  bool stop = false;
  bool liveStop = false;
  int page = 0;
  int livePage = 0;
  int perPage = 13;
  int livePerPage = 10;
  final List<MarketList> marketList = [];
  final List<MarketList> liveStoreList = [];
  GroceryHomeModel? groceryHomeModel;
  MarketListModel? categoryModel;
  List<OrderLateNotificationsModel> orderNotification = [];

  late final AppLifecycleListener _listener;
  // bool _hasShownDeliveryBottomSheet = false;
  final ScrollController _scrollController = ScrollController();

  // Grocery market tab new feature
  final favStoreList = ValueNotifier<List<FavStoreModel>>([]);

  final storeByCategoryList = ValueNotifier<List<StoreByCategoryModel>>([]);
  final randomStoreList = ValueNotifier<List<RandomStoreModel>>([]);
  String? storeName;
  String? contactNo;

  @override
  void initState() {
    super.initState();
    try {
      _listener = AppLifecycleListener(
        onResume: () {
          init();
          log("onResume");
        },
        onRestart: () {
          init();
          log("onRestart");
        },
        onShow: () {
          init();
          log("onShow");
        },
      );
      EasyDebounce.debounce('address_bottom_sheet', Duration(seconds: 1), () async {
        // Utility.toast(message: 'Before Address list api call');
        controller.displayAddressList().then((value) => {
              // Utility.toast(
              //     message:
              //         'CON 1 ${((controller.addressList.length <= 0) && ((PreferenceManagerUtils.getUserIsAuthorized() != SharePreData.key_unauthorized)) && !widget.isFromDynamicLink)} Con 2  ${((controller.addressList.length > 0) && ((PreferenceManagerUtils.getUserIsAuthorized() != SharePreData.key_unauthorized)) && !widget.isFromDynamicLink && PreferenceManagerUtils.getYourAddress() == '')}'),
              if (((controller.addressList.length <= 0) &&
                      ((PreferenceManagerUtils.getUserIsAuthorized() != SharePreData.key_unauthorized)) &&
                      !widget.isFromDynamicLink) ||
                  ((controller.addressList.length > 0) &&
                      ((PreferenceManagerUtils.getUserIsAuthorized() != SharePreData.key_unauthorized)) &&
                      !widget.isFromDynamicLink &&
                      PreferenceManagerUtils.getYourAddress() == ''))
                {
                  if (!widget.isFromOrderSummaryPage)
                    {
                      openAddressBottomSheet(context, controller),
                    }
                }
              // else
              //   {
              //     // If no address bottom sheet is shown, show delivery time bottom sheet directly
              //     Future.delayed(Duration(milliseconds: 1500), () {
              //       if (!_hasShownDeliveryBottomSheet && mounted) {
              //         _hasShownDeliveryBottomSheet = true;
              //         showDeliveryTimeBottomSheet(context);
              //       }
              //     })
              //   }
            });
        // Utility.toast(message: 'After Address list api call');
      });

      init();
    } catch (e) {
      // Utility.toast(
      //   message: 'CASE 1 : **' + e.toString(),
      // );
    }
  }

  void showDeliveryTimeBottomSheet(BuildContext context) {
    // Show the banner above the bottom navigation bar
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) {
        // Calculate the position to show above the bottom navigation bar
        // The bottom nav height is approximately 80-90 pixels
        return DeliveryTimeBottomSheet(
          onGetStarted: () {
            Navigator.pop(context);
          },
          onClose: () {
            Navigator.pop(context);
          },
        );
      },
    );
  }

  void showOrderStoreBottomSheet(BuildContext context) {
    // Show the banner above the bottom navigation bar
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      isDismissible: false,
      builder: (context) {
        return StoreBusySheet(storeName: storeName!, contactNo: contactNo!);
      },
    ).whenComplete(() async {
      var preferences = MySharedPref();
      await preferences.clearOrderLateNotification();
    });
  }

  init() async {
    try {
      // var preferences = MySharedPref();
      // addressModelFORCITY = await preferences.getSelectedAddressModel(SharePreData.key_SelectedAddressModel);
      log('QQQQQQQQQQQQQQQQ');
      // Utility.toast(message: 'Before loading true');

      isNewLoading.value = true;

      // Utility.toast(message: 'After loading true');

      StaticData.addProductList.clear();

      // Utility.toast(message: 'Before home api call');

      controller.callHomeAPI();
      // Utility.toast(message: 'After home api call');
      // Utility.toast(message: 'Before grocery home api call');
      getRandomStore();
      getGroceryHomeApis();
      // Utility.toast(message: 'After grocery home api call');
      // Utility.toast(message: 'Before live store api call');
      log(isNewLoading.value.toString() + "new loading ____________________1");
      await getLiveStoreList(isFirst: true);

      // Utility.toast(message: 'After live store api call');

      log(isNewLoading.value.toString() + "new loading ____________________2");
      // Utility.toast(message: 'Before store api call');
      await getStoreByCategoryList();

      log('please fav AAAAAAAAAA');
      await getFavStroreList();
      log('please fav BBBBBBBBBB');
      await getStoreList(isFirst: true);
      // Utility.toast(message: 'After store api call');
      isNewLoading.value = false;
      log(isNewLoading.value.toString() + "new loading ____________________3");
      // initializeVideoPlayerFuture = videoController?.initialize();
      // _notify();
    } catch (e) {
      // Utility.toast(
      //   message: 'CASE 2 : **' + e.toString(),
      // );
      isNewLoading.value = false;
    }
  }

  Future<void> getRandomStore() async {
    var preferences = MySharedPref();
    SigninModel? myModel = await preferences.getSignInModel(SharePreData.key_SaveSignInModel);

    var request = <String, dynamic>{};
    request['user_id'] = myModel!.data!.id.toString();
    request['device_token'] = ((myModel.data?.id ?? 0).toString() == "0") ? "" : PreferenceManagerUtils.getFcmToken();
    request['city_id'] = PreferenceManagerUtils.getCityId().toString();

    log(request.toString());
    try {
      RandomStoreResponse response =
          RandomStoreResponse.fromJson(await ApiManager().postCall(urlGroceryRandomStore, request, context));
      log(response.toJson().toString());

      if (response.success == true && response.data != null) {
        randomStoreList.value = response.data ?? [];
        _notify();
      }
    } catch (e) {
      log('CASE 5 : **' + e.toString());
    }
  }

  Future<void> getGroceryHomeApis() async {
    try {
      var preferences = MySharedPref();
      final data = await preferences.getSignInModel(SharePreData.key_SaveSignInModel);
      if (data != null) {
        SigninModel? myModel = await preferences.getSignInModel(SharePreData.key_SaveSignInModel);

        var request = <String, dynamic>{};
        request['user_id'] = myModel!.data!.id.toString();
        request['city_id'] = PreferenceManagerUtils.getCityId().toString();

        GroceryHomeResponse commonResponse =
            GroceryHomeResponse.fromJson(await ApiManager().getCall(groceryHomeApi, request, context));
        if (commonResponse.statusCode == 0) {
          Navigator.pushAndRemoveUntil(
            context,
            MaterialPageRoute(
              builder: (context) => Welcome(),
            ),
            (route) => false,
          );
        }

        if (commonResponse.success == true) {
          videoController?.dispose();
          videoController = null;
          groceryHomeModel = commonResponse.data;
          log('----AAAABBB--${groceryHomeModel?.mainAddBanner?.file} --- ${groceryHomeModel?.mainAddBanner?.fileType}');

          if (groceryHomeModel?.mainAddBanner?.fileType == 'video') {
            log('VideoController is initialized Started');
            videoController = VideoPlayerController.networkUrl(Uri.parse(groceryHomeModel?.mainAddBanner?.file ?? ''));
            await videoController?.initialize();
            videoController?.setVolume(0);
            videoController?.play();
            videoController?.setLooping(true);
            log('VideoController is ${videoController?.value.duration}');
          }

          _notify();
        }
      }
    } catch (e, s) {
      // Utility.toast(
      //   message: 'CASE 4 : **' + e.toString() + "\n " + s.toString(),
      // );
      log('CASE 4 : **' + e.toString() + "\n " + s.toString());
    }
  }

  void _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    super.dispose();
    _debouncer.dispose();
    videoController?.dispose();
    _scrollController.dispose();
  }

  Future<void> getStoreList({required bool isFirst}) async {
    try {
      if (await ApiManager.checkInternet()) {
        if (isFirst) {
          isLoading.value = true;
        } else {
          isPageLoading.value = true;
        }
        page += 1;
        var preferences = MySharedPref();
        //User Personal details
        SigninModel? myModel = await preferences.getSignInModel(SharePreData.key_SaveSignInModel);

        AddressDatum? addressModel = await preferences.getSelectedAddressModel(SharePreData.key_SelectedAddressModel);
        log('selectCityId.value.toString() :: ' + (myModel?.data?.id ?? "").toString());
        dynamic body = {
          'country_id': (addressModel?.countryId ?? 0).toString(),
          'user_id': (myModel?.data?.id ?? 0).toString(),
          'device_token': PreferenceManagerUtils.getFcmToken(),
          'city': (addressModel?.cityId ?? 0).toString(),
          'page': page.toString(),
          // 'per_page': perPage.toString(),
          // 'is_live': '0',
        };

        String url = urlBase + urlStoreList;
        final apiReq = Request();
        await apiReq.postAPIwithoutBearer(url, body).then((value) {
          http.StreamedResponse res = value;

          if (res.statusCode == 200) {
            res.stream.bytesToString().then((value) async {
              Map<String, dynamic> userModel = json.decode(value);
              BaseModel model = BaseModel.fromJson(userModel);
              if (model.statusCode == 0) {
                final logOutUser = SessionOut();
                logOutUser.userLogout();
              } else if (model.statusCode == 200) {
                categoryModel = MarketListModel.fromJson(userModel);

                if (categoryModel?.orderLateNotifications?.isNotEmpty ?? false) {
                  orderNotification = categoryModel?.orderLateNotifications ?? [];
                  preferences.setOrderLateNotification(orderNotification.firstOrNull!);
                  storeName = orderNotification.firstOrNull?.store?.name;
                  contactNo = orderNotification.firstOrNull?.store?.contactNumber;
                  showOrderStoreBottomSheet(context);
                  setState(() {});
                } else {
                  final orderLateNotification = preferences.getOrderLateNotification();
                  if (orderLateNotification != null) {
                    storeName = orderLateNotification.store?.name;
                    contactNo = orderLateNotification.store?.contactNumber;
                    showOrderStoreBottomSheet(context);
                  }
                }
                log('please fav DDDDDDDDDD ${orderNotification.length}');
                //marketList.addAll(categoryModel.marketList!);
                marketList.addAll(categoryModel!.marketList!);
                _notify();

                log(marketList.length.toString() + " ---- store length");
              }
              if (isFirst) {
                isLoading.value = false;
              } else {
                isPageLoading.value = false;
              }
            });
          } else {
            noDataLogic(page);
            print(res.reasonPhrase);
          }
        });
      } else {
        if (mounted) {
          Utility.toast(message: 'No Internet Connection');
        }
      }
    } catch (e) {
      // Utility.toast(
      //   message: 'CASE 6 : **' + e.toString(),
      // );
    }
  }

  Future<void> getLiveStoreList({required bool isFirst}) async {
    try {
      if (await ApiManager.checkInternet()) {
        if (isFirst) {
          isLoading.value = true;
        } else {
          isLivePageLoading.value = true;
        }
        livePage += 1;
        var preferences = MySharedPref();
        //User Personal details
        SigninModel? myModel = await preferences.getSignInModel(SharePreData.key_SaveSignInModel);

        AddressDatum? addressModel = await preferences.getSelectedAddressModel(SharePreData.key_SelectedAddressModel);
        log('selectCityId.value.toString() :: ' + (myModel?.data?.id ?? "").toString());
        dynamic body = {
          'country_id': (addressModel?.countryId ?? 0).toString(),
          'user_id': (myModel?.data?.id ?? 0).toString(),
          'city': (addressModel?.cityId ?? 0).toString(),
          // 'page': livePage.toString(),
          // 'per_page': livePerPage.toString(),
        };

        String url = urlBase + urlLiveStoreList;
        final apiReq = Request();
        await apiReq.postAPIwithoutBearer(url, body).then((value) {
          http.StreamedResponse res = value;

          if (res.statusCode == 200) {
            res.stream.bytesToString().then((value) async {
              Map<String, dynamic> userModel = json.decode(value);
              BaseModel model = BaseModel.fromJson(userModel);

              if (model.statusCode == 0) {
                final logOutUser = SessionOut();
                logOutUser.userLogout();
              } else if (model.statusCode == 200) {
                MarketListModel categoryModel = MarketListModel.fromJson(userModel);

                liveStoreList.addAll(categoryModel.marketList!);
                _notify();
              }
              if (isFirst) {
                isLoading.value = false;
              } else {
                isLivePageLoading.value = false;
              }
            });
          } else {
            noLiveDataLogic(page);
            print(res.reasonPhrase);
          }
        });
      } else {
        if (mounted) {
          Utility.toast(message: 'No Internet Connection');
        }
      }
    } catch (e) {
      // Utility.toast(
      //   message: 'CASE 5 : **' + e.toString(),
      // );
    }
  }

  void noDataLogic(pageNumber) {
    page = pageNumber - 1;
    stop = true;
    _notify();
  }

  void noLiveDataLogic(pageNumber) {
    livePage = pageNumber - 1;
    liveStop = true;
    _notify();
  }

  Future<void> getFavStroreList() async {
    var preferences = MySharedPref();
    SigninModel? myModel = await preferences.getSignInModel(SharePreData.key_SaveSignInModel);

    var request = <String, dynamic>{};
    request['user_id'] = myModel!.data!.id.toString();
    log(request.toString());
    try {
      GroceryFavStoreResponse response =
          GroceryFavStoreResponse.fromJson(await ApiManager().getCall(urlGroceryFavStore, request, context));
      log(response.toJson().toString());

      if (response.success == true && response.data != null) {
        favStoreList.value = response.data ?? [];

        _notify();
      }
    } catch (e) {}
  }

  Future<void> getStoreByCategoryList() async {
    var preferences = MySharedPref();
    SigninModel? myModel = await preferences.getSignInModel(SharePreData.key_SaveSignInModel);
    log('AAAAAAAAAAAAA by category');
    var request = <String, String>{
      'user_id': myModel!.data!.id.toString(),
      'device_token': PreferenceManagerUtils.getFcmToken(),
    };

    log(request.toString());
    log('BBBBBBBBBBBBB by category');
    try {
      StoresByCategoryResponse response =
          StoresByCategoryResponse.fromJson(await ApiManager().postCall(urlGroceryStoreByCategory, request, context));
      log(response.toJson().toString());
      log('CCCCCCCCCCCCC by category');

      if (response.success == true && response.data != null) {
        log('DDDDDDDDDDDDD by category');
        storeByCategoryList.value = response.data ?? [];
        _notify();
      }
    } catch (e) {
      log('EEEEEEEEEEEEEE by category $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        body: Stack(
          children: [
            ValueListenableBuilder<bool>(
                valueListenable: isNewLoading,
                builder: (context, value, _) {
                  if (value) {
                    return LoadingScreen(text: '', image: groceryGif);
                  }
                  return CustomScrollView(
                    scrollBehavior: MyCustomScrollBehavior(),
                    // physics: const ClampingScrollPhysics(),
                    controller: _scrollController,
                    slivers: [
                      SliverToBoxAdapter(
                        child: Stack(
                          alignment: Alignment.bottomCenter,
                          children: [
                            InkWell(
                              onTap: () {
                                log("test web view ___________ before");

                                final link = controller.displayAdvData?.data?.mainAddModel?.link ?? '';

                                if (link.trim().isEmpty ||
                                    controller.displayAdvData?.data?.mainAddModel?.file == null) {
                                  return;
                                }
                                final url = Uri.tryParse(link);
                                if (!(url?.hasAbsolutePath ?? true)) {
                                  return;
                                }
                                Navigator.push(
                                  context,
                                  CustomPageRoute(
                                    RestaurantVideoAdWebView2(
                                      restaurantListController: controller,
                                    ),
                                  ),
                                );
                              },
                              child: Container(
                                  margin: EdgeInsets.only(bottom: MediaQuery.sizeOf(context).height * 0.032),
                                  child: Column(
                                    children: [
                                      groceryHomeModel?.mainAddBanner?.file == null
                                          ? SizedBox()
                                          : groceryHomeModel?.mainAddBanner?.fileType == "video"
                                              ? Visibility(
                                                  visible:
                                                      videoController != null && videoController!.value.isInitialized,
                                                  replacement: SizedBox(
                                                    height: 200,
                                                    child: Center(
                                                      child: Container(
                                                        height: 200,
                                                        width: MediaQuery.of(context).size.width,
                                                        color: Color(0Xffebebeb),
                                                      ),
                                                    ),
                                                  ),
                                                  child: Center(
                                                    child: AspectRatio(
                                                      aspectRatio: videoController!.value.aspectRatio,
                                                      child: Stack(
                                                        children: [
                                                          VideoPlayer(videoController!),
                                                          Positioned(
                                                            bottom: 16,
                                                            right: 16,
                                                            child: GestureDetector(
                                                              onTap: () {
                                                                setState(() {
                                                                  // Toggle mute state on tap
                                                                  isMuted = !isMuted;
                                                                  videoController?.setVolume(isMuted ? 0.0 : 1.0);
                                                                });
                                                              },
                                                              child: Icon(
                                                                isMuted ? Icons.volume_off : Icons.volume_up,
                                                                color: Colors.white,
                                                                size: 20,
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                )
                                              // SizedBox(
                                              //         height: 200,
                                              //         child: Center(
                                              //           child: Container(
                                              //             height: 200,
                                              //             width: MediaQuery.of(context).size.width,
                                              //             color: Color(0Xffebebeb),
                                              //           ),
                                              //         ),
                                              //       )
                                              : CachedNetworkImage(
                                                  key: UniqueKey(),
                                                  imageUrl: groceryHomeModel?.mainAddBanner?.file ?? "",
                                                  fit: BoxFit.cover,
                                                  width: double.infinity,
                                                  //  height: 246.h,
                                                  placeholder: (context, url) => Image.asset(
                                                    backgroundPlaceHolder,
                                                    fit: BoxFit.cover,
                                                    width: double.infinity,
                                                    height: 246.h,
                                                  ),
                                                  errorWidget: (context, url, error) => Image.asset(
                                                    backgroundPlaceHolder,
                                                    fit: BoxFit.cover,
                                                    width: double.infinity,
                                                    height: 246.h,
                                                  ),
                                                ),
                                    ],
                                  )),
                            ),
                            Container(
                              // height: 58.2.h,
                              padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 12.w),
                              decoration: commonShadow,
                              margin: EdgeInsets.only(
                                  left: 16.w,
                                  right: 16.w,
                                  top: controller.displayAdvData?.data?.mainAddModel?.link == null ||
                                          controller.displayAdvData?.data?.mainAddModel?.file == null
                                      ? 50
                                      : 0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                    icon_location_home_new,
                                    width: 21,
                                    height: 21,
                                  ),
                                  SizedBox(
                                    width: 12.w,
                                  ),
                                  Expanded(
                                    child: InkWell(
                                      onTap: () async {
                                        // showLoadingDialog(context: context);
                                        if ((PreferenceManagerUtils.getUserIsAuthorized() !=
                                            SharePreData.key_unauthorized)) {
                                          if (!widget.isFromOrderSummaryPage)
                                            openAddressBottomSheet(context, controller);
                                        }
                                        // hideLoadingDialog(context: context);
                                      },
                                      child: PreferenceManagerUtils.getUserName() == ''
                                          ? Row(
                                              children: [
                                                Text(
                                                  SharePreData.strGuest,
                                                  style: AppTextStyle.smallSemiBold,
                                                ),
                                                SizedBox(
                                                  width: 4.w,
                                                ),
                                                SvgPicture.asset(
                                                  icon_down_arrow_new,
                                                ),
                                              ],
                                            )
                                          : Column(
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                PreferenceManagerUtils.getUserName() == ''
                                                    ? Row(
                                                        children: [
                                                          Text(
                                                            SharePreData.strGuest,
                                                            style: AppTextStyle.smallSemiBold,
                                                          ),
                                                          SizedBox(
                                                            width: 5.w,
                                                          ),
                                                          SvgPicture.asset(
                                                            icon_down_arrow_new,
                                                          ),
                                                        ],
                                                      )
                                                    : Row(
                                                        children: [
                                                          Flexible(
                                                            child: Text(
                                                              "${PreferenceManagerUtils.getUserName()}",
                                                              maxLines: 1,
                                                              overflow: TextOverflow.ellipsis,
                                                              style: AppTextStyle.smallSemiBold,
                                                            ),
                                                          ),
                                                          SizedBox(
                                                            width: 5.w,
                                                          ),
                                                          SvgPicture.asset(
                                                            icon_down_arrow_new,
                                                          ),
                                                        ],
                                                      ),
                                                SizedBox(
                                                  height: 3.h,
                                                ),
                                                PreferenceManagerUtils.getYourAddress() == ''
                                                    ? Text(
                                                        "Select address",
                                                        style: AppTextStyle.xSmallRegular,
                                                      )
                                                    : Text(
                                                        "${PreferenceManagerUtils.getYourAddress()}",
                                                        maxLines: 1,
                                                        style: AppTextStyle.xSmallRegular,
                                                      ),
                                              ],
                                            ),
                                    ),
                                  ),
                                  SizedBox(
                                    width: 11.w,
                                  ),
                                  InkWell(
                                    onTap: () {
                                      if ((PreferenceManagerUtils.getUserIsAuthorized() !=
                                          SharePreData.key_unauthorized)) {
                                        Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) => const NotificationListView(),
                                            ));
                                      } else {
                                        onWarningOfUnAuthorizedUser(context);
                                      }
                                    },
                                    child: Stack(
                                      alignment: Alignment.topRight,
                                      children: [
                                        Container(
                                            width: 32.w,
                                            height: 32.h,
                                            decoration: BoxDecoration(shape: BoxShape.circle, color: grey_100),
                                            padding: EdgeInsets.all(6),
                                            child: SvgPicture.asset(icon_notification_new, height: 40.h, width: 40.w)),
                                      ],
                                    ),
                                  ),
                                  SizedBox(
                                    width: 11.w,
                                  ),
                                  InkWell(
                                    onTap: () async {
                                      await Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) => GroceryChatsListPage(),
                                        ),
                                      );
                                    },
                                    child: ValueListenableBuilder<SigninModel?>(
                                        valueListenable: loginModel,
                                        builder: (_, value, __) {
                                          return StreamBuilder<int?>(
                                            initialData: 0,
                                            stream: GroceriesFirebaseChatService.getTotalBadges(
                                                loginUser: int.tryParse(PreferenceManagerUtils.getUserId()) ??
                                                    value?.data?.id),
                                            builder: (_, snapshot) {
                                              return Stack(
                                                alignment: Alignment.center,
                                                children: [
                                                  Container(
                                                    margin: EdgeInsets.only(right: 10),
                                                    width: 35.w,
                                                    height: 35.h,
                                                    padding: EdgeInsets.all(5),
                                                    child: SvgPicture.asset(
                                                      message_icon,
                                                      width: 40.w,
                                                      height: 40.h,
                                                      color: Colors.black,
                                                    ),
                                                  ),
                                                  if (snapshot.data != null && snapshot.data != 0)
                                                    Positioned(
                                                      top: 0,
                                                      right: 10,
                                                      child: Badge.count(count: snapshot.data ?? 0),
                                                    ),
                                                ],
                                              );
                                            },
                                          );
                                        }),
                                  ),
                                  Center(
                                    child: Container(
                                      width: 32.w,
                                      height: 32.h,
                                      child: SizedBox(
                                        width: 24.w,
                                        height: 24.h,
                                        child: PreferenceManagerUtils.getUserProfile() != ''
                                            ? CircularProfileAvatar(
                                                '',
                                                child: CachedNetworkImage(
                                                  fit: BoxFit.cover,
                                                  imageUrl: PreferenceManagerUtils.getUserProfile(),
                                                  placeholder: (context, url) => SvgPicture.asset(
                                                    add_placeholder,
                                                    width: 24.w,
                                                    height: 24.h,
                                                  ),
                                                ),
                                              )
                                            : ClipRRect(
                                                borderRadius: BorderRadius.circular(100),
                                                child: Image.asset(
                                                  // geocery_app_logo,
                                                  iconLovegrubLogo,
                                                  fit: BoxFit.cover,
                                                  alignment: Alignment.center,
                                                ),
                                              ),

                                        // ),
                                      ),
                                      decoration: BoxDecoration(shape: BoxShape.circle, color: grey_e9ecec),
                                    ),
                                  ),
                                  Visibility(
                                    visible: true,
                                    child: Stack(
                                      alignment: Alignment.topRight,
                                      children: [],
                                    ),
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                      SliverAppBar(
                        automaticallyImplyLeading: false,
                        backgroundColor: AppColors.white,
                        toolbarHeight: 72.h,
                        primary: false,
                        pinned: true,
                        title: InkWell(
                          onTap: () {
                            Get.to(() => CategorySearchScreen());
                          },
                          child: Container(
                            margin: EdgeInsets.symmetric(vertical: 16.h),
                            padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
                            decoration:
                                BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(8.r)), color: grey_50),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              mainAxisSize: MainAxisSize.max,
                              children: [
                                SvgPicture.asset(
                                  iconSearchGrey,
                                  width: 16.w,
                                  height: 16.h,
                                  color: Colors.black,
                                ),
                                SizedBox(
                                  width: 14.w,
                                ),
                                Expanded(
                                  child: TextField(
                                    style: TextStyle(
                                        color: grey_96a6a3, fontFamily: SF_PRO_DISPLAY_MEDIUM, fontSize: 14.sp),
                                    decoration: new InputDecoration(
                                      isDense: true,
                                      contentPadding: EdgeInsets.zero,
                                      hintText: "Search grocery stores...",
                                      hintStyle: AppTextStyle.smallRegular.copyWith(color: grey_600),
                                      labelStyle: AppTextStyle.smallRegular,
                                      border: InputBorder.none,
                                    ),
                                    keyboardType: TextInputType.text,
                                    textInputAction: TextInputAction.done,
                                    enabled: false,
                                    // onChanged: (val) {
                                    //   marketController.displayMarketList("$val");
                                    // },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      SliverToBoxAdapter(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Padding(
                                padding: const EdgeInsets.only(left: 16),
                                child: Row(
                                  children: groceryHomeModel?.categories
                                          ?.map((e) => categoryView(
                                                onTap: () {
                                                  Get.to(() => CategorySearchScreen(
                                                        // categoryModel: groceryHomeModel?.categories?[index],
                                                        categoryId: e.id,
                                                        categoryName: e.name,
                                                      ));
                                                },
                                                image: e.image,
                                                title: e.name,
                                              ))
                                          .toList() ??
                                      [],
                                ),
                              ),
                            ),
                            SizedBox(height: 20),
                            if (liveStoreList.isNotEmpty) ...[
                              Padding(
                                padding: EdgeInsets.only(left: 16.w, right: 16.w, bottom: 12),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text('Live Stores', style: AppTextStyle.mediumBold),
                                    InkWell(
                                      onTap: () {
                                        Navigator.push(context,
                                            MaterialPageRoute(builder: (context) => const SearchLiveStreamPage()));
                                      },
                                      child: Container(
                                          padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 11.w),
                                          decoration: BoxDecoration(
                                            color: grey_50,
                                            borderRadius: BorderRadius.circular(100.r),
                                          ),
                                          child: Text(
                                            "Live Stream",
                                            style: AppTextStyle.xSmallMedium,
                                          )),
                                    ),
                                  ],
                                ),
                              ),
                              SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Padding(
                                  padding: const EdgeInsets.only(left: 16, bottom: 20),
                                  child: Row(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: liveStoreList
                                        .map(
                                          (store) => liveStoreItem(store),
                                        )
                                        .toList(),
                                  ),
                                ),
                              ),
                              Divider(
                                height: 1,
                                thickness: 1,
                                color: grey_100,
                              ),
                              SizedBox(
                                height: 20,
                              ),
                            ],
                            ValueListenableBuilder(
                                valueListenable: storeByCategoryList,
                                builder: (context, list, _) {
                                  if (list.isEmpty) return const SizedBox();
                                  return Flexible(
                                    child: ListView.separated(
                                        padding: EdgeInsets.zero,
                                        shrinkWrap: true,
                                        primary: false,
                                        itemCount: list.length,
                                        itemBuilder: (context, index) {
                                          return Column(
                                            children: [
                                              InkWell(
                                                onTap: () {
                                                  Get.to(() => CategorySearchScreen(
                                                        categoryId: list[index].categoryId.toString(),
                                                        categoryName: list[index].title,
                                                      ));
                                                },
                                                child: Padding(
                                                  padding: const EdgeInsets.symmetric(horizontal: 16),
                                                  child: Row(
                                                    children: [
                                                      Expanded(
                                                        flex: 1,
                                                        child: Column(
                                                          mainAxisAlignment: MainAxisAlignment.start,
                                                          crossAxisAlignment: CrossAxisAlignment.start,
                                                          children: [
                                                            CustomTitleRestaurant(
                                                              leftPadding: 0,
                                                              rightPadding: 0,
                                                              bottomPadding: 0,
                                                              textStyle: AppTextStyle.mediumBold,
                                                              titleText: list[index].title ?? "",
                                                            ),
                                                            Text(list[index].subTitle ?? "",
                                                                style: AppTextStyle.xSmallMedium
                                                                    .copyWith(color: AppColors.textMuted),
                                                                textAlign: TextAlign.left)
                                                          ],
                                                        ),
                                                      ),
                                                      SvgPicture.asset(
                                                        icon_home_next_circle,
                                                        width: 31,
                                                        height: 31,
                                                      )
                                                    ],
                                                  ),
                                                ),
                                              ),
                                              Align(
                                                alignment: Alignment.topLeft,
                                                child: SingleChildScrollView(
                                                  scrollDirection: Axis.horizontal,
                                                  child: Padding(
                                                    padding: const EdgeInsets.only(left: 16),
                                                    child: Row(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      mainAxisAlignment: MainAxisAlignment.start,
                                                      children: list[index]
                                                          .stores!
                                                          .map((e) => newSportsOfWeek(store: e))
                                                          .toList(),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          );
                                        },
                                        separatorBuilder: (context, index) => Column(
                                              children: [
                                                SizedBox(
                                                  height: 20,
                                                ),
                                                Divider(
                                                  height: 1,
                                                  thickness: 1,
                                                  color: grey_100,
                                                ),
                                                SizedBox(
                                                  height: 20,
                                                ),
                                              ],
                                            )),
                                  );
                                }),
                            ValueListenableBuilder<List<RandomStoreModel>>(
                                valueListenable: randomStoreList,
                                builder: (context, list, _) {
                                  return list.isNotEmpty
                                      ? Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            SizedBox(
                                              height: 20,
                                            ),
                                            Divider(
                                              height: 1,
                                              thickness: 1,
                                              color: grey_100,
                                            ),
                                            Flexible(
                                              child: ListView.separated(
                                                separatorBuilder: (context, index) {
                                                  return Column(
                                                    mainAxisSize: MainAxisSize.min,
                                                    children: [
                                                      SizedBox(
                                                        height: 20,
                                                      ),
                                                      Divider(
                                                        height: 1,
                                                        thickness: 1,
                                                        color: grey_100,
                                                      ),
                                                      SizedBox(
                                                        height: 20,
                                                      ),
                                                    ],
                                                  );
                                                },
                                                shrinkWrap: true,
                                                primary: false,
                                                padding: EdgeInsets.only(top: 20),
                                                itemCount: list.length,
                                                itemBuilder: (context, index) {
                                                  final store = list[index];
                                                  return InkWell(
                                                    onTap: () {
                                                      final distanceValue =
                                                          double.tryParse(store.distance?.toString() ?? '') ?? 0;

                                                      Get.to(
                                                        StoreDetailUi(
                                                          storeName: store.name ?? '',
                                                          storeStautsText: store.storeStatus ?? '',
                                                          overseas: 0,
                                                          storeId: store.id?.toString() ?? '',
                                                          isStoreClose: store.storeStatus == 'Closed',
                                                          isStoreoutOfRange: distanceValue > 20,
                                                        ),
                                                      );
                                                    },
                                                    child: Column(
                                                      mainAxisSize: MainAxisSize.min,
                                                      children: [
                                                        Padding(
                                                          padding: const EdgeInsets.symmetric(horizontal: 16),
                                                          child: Row(
                                                            children: [
                                                              ClipRRect(
                                                                borderRadius: BorderRadius.circular(100),
                                                                child: CachedNetworkImage(
                                                                  key: UniqueKey(),
                                                                  width: 48.w,
                                                                  height: 48.h,
                                                                  imageUrl: store.image ?? '',
                                                                  fit: BoxFit.fill,
                                                                  placeholder: (context, url) => Image.asset(
                                                                    treeNewIcon,
                                                                  ),
                                                                  errorWidget: (context, url, error) => Image.asset(
                                                                    treeNewIcon,
                                                                  ),
                                                                ),
                                                              ),
                                                              SizedBox(
                                                                width: 16,
                                                              ),
                                                              Expanded(
                                                                flex: 1,
                                                                child: Column(
                                                                  mainAxisAlignment: MainAxisAlignment.start,
                                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                                  mainAxisSize: MainAxisSize.min,
                                                                  children: [
                                                                    CustomTitleRestaurant(
                                                                      // topPadding: 16.h,
                                                                      leftPadding: 0,
                                                                      rightPadding: 0,
                                                                      bottomPadding: 0,
                                                                      textStyle: AppTextStyle.mediumBold,
                                                                      titleText: index == 0
                                                                          ? 'You might find hidden gems'
                                                                          : 'Try something different',
                                                                    ),
                                                                    Row(
                                                                      mainAxisSize: MainAxisSize.min,
                                                                      children: [
                                                                        Flexible(
                                                                          child: Text("${store.name} - ",
                                                                              maxLines: 1,
                                                                              style: AppTextStyle.xSmallMedium
                                                                                  .copyWith(color: AppColors.textMuted),
                                                                              textAlign: TextAlign.left),
                                                                        ),
                                                                        Text(store.storeStatus ?? '',
                                                                            style: AppTextStyle.xSmallMedium.copyWith(
                                                                                color: store.storeStatus == 'Open'
                                                                                    ? AppColors.cloverGreen
                                                                                    : AppColors.fireEngineRed),
                                                                            textAlign: TextAlign.left),
                                                                      ],
                                                                    )
                                                                  ],
                                                                ),
                                                              ),
                                                              SvgPicture.asset(
                                                                icon_home_next_circle,
                                                                width: 31,
                                                                height: 31,
                                                              )
                                                            ],
                                                          ),
                                                        ),
                                                        if (store.products?.isNotEmpty ?? false)
                                                          SingleChildScrollView(
                                                            scrollDirection: Axis.horizontal,
                                                            child: Padding(
                                                              padding: const EdgeInsets.only(top: 20, left: 16),
                                                              child: Row(
                                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                                mainAxisAlignment: MainAxisAlignment.start,
                                                                children: store.products!
                                                                    .map((e) => TopTenSportsWeekItemWidget(
                                                                          onTap: () {
                                                                            final distanceValue = double.tryParse(
                                                                                    store.distance?.toString() ?? '') ??
                                                                                0;
                                                                            Get.to(
                                                                              StoreDetailUi(
                                                                                storeName: store.name ?? '',
                                                                                storeStautsText:
                                                                                    store.storeStatus ?? '',
                                                                                overseas: 0,
                                                                                storeId: store.id?.toString() ?? '',
                                                                                isStoreClose:
                                                                                    store.storeStatus == 'Closed',
                                                                                isStoreoutOfRange: distanceValue > 20,
                                                                              ),
                                                                            );
                                                                          },
                                                                          product: e,
                                                                        ))
                                                                    .toList(),
                                                              ),
                                                            ),
                                                          ),
                                                      ],
                                                    ),
                                                  );
                                                },
                                              ),
                                            ),
                                          ],
                                        )
                                      : SizedBox.shrink();
                                }),
                            ValueListenableBuilder(
                                valueListenable: favStoreList,
                                builder: (context, list, _) {
                                  if (list.isEmpty) return const SizedBox();
                                  return Column(
                                    children: [
                                      SizedBox(
                                        height: 20,
                                      ),
                                      Divider(
                                        height: 1,
                                        thickness: 1,
                                        color: grey_100,
                                      ),
                                      SizedBox(
                                        height: 20,
                                      ),
                                      Padding(
                                        padding: EdgeInsets.fromLTRB(16.w, 0.h, 16.w, 16.h),
                                        child: Row(
                                          children: [
                                            Expanded(
                                              child: RichText(
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                                text: TextSpan(
                                                  children: [
                                                    TextSpan(
                                                      text: 'Favorite Stores ',
                                                      style: AppTextStyle.mediumBold,
                                                    ),
                                                    // TextSpan(
                                                    //   text: groceryHomeModel?.cityName,
                                                    //   //PreferenceManagerUtils.getYourAddress(),
                                                    //   style: AppTextStyle.largeBold
                                                    //       .copyWith(color: AppColors.checkboxSelectedColor),
                                                    // )
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      ListView.separated(
                                        shrinkWrap: true,
                                        primary: false,
                                        itemCount: list.length,
                                        separatorBuilder: (context, index) => 20.verticalSpace,
                                        padding: EdgeInsets.zero,
                                        itemBuilder: (context, index) {
                                          final isLast = index == (list.length - 1);
                                          return storeItemVew(index, isLast);
                                        },
                                      ),
                                    ],
                                  );
                                }),
                            SizedBox(
                              height: 20,
                            ),
                            Divider(
                              height: 1,
                              thickness: 1,
                              color: grey_100,
                            ),
                            SizedBox(
                              height: 20,
                            ),
                            // Padding(
                            //   padding: EdgeInsets.fromLTRB(16.w, 0.h, 16.w, 16.h),
                            //   child: Row(
                            //     children: [
                            //       Expanded(
                            //         child: RichText(
                            //           maxLines: 1,
                            //           overflow: TextOverflow.ellipsis,
                            //           text: TextSpan(
                            //             children: [
                            //               TextSpan(
                            //                 text: 'Stores in ',
                            //                 style: AppTextStyle.largeBold,
                            //               ),
                            //               TextSpan(
                            //                 text: groceryHomeModel?.cityName,
                            //                 //PreferenceManagerUtils.getYourAddress(),
                            //                 style:
                            //                     AppTextStyle.largeBold.copyWith(color: AppColors.checkboxSelectedColor),
                            //               )
                            //             ],
                            //           ),
                            //         ),
                            //       ),
                            //     ],
                            //   ),
                            // ),
                            ValueListenableBuilder<bool>(
                                valueListenable: isLoading,
                                builder: (context, loading, _) {
                                  if (loading) return CommonStoreShimmerListDataP3();
                                  if ((!loading && marketList.isEmpty) && (!loading && liveStoreList.isEmpty))
                                    return noDatFound(context);
                                  return Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: EdgeInsets.only(left: 16.w, bottom: 16),
                                        child: RichText(
                                          text: TextSpan(
                                            children: [
                                              TextSpan(
                                                text: 'All stores in ',
                                                style: AppTextStyle.mediumBold,
                                              ),
                                              TextSpan(
                                                text: groceryHomeModel?.cityName,
                                                style: AppTextStyle.mediumBold
                                                    .copyWith(color: AppColors.checkboxSelectedColor),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                      if (marketList.isNotEmpty)
                                        ListView.separated(
                                          physics: ClampingScrollPhysics(),
                                          scrollDirection: Axis.vertical,
                                          primary: false,
                                          shrinkWrap: true,
                                          padding: EdgeInsets.zero,
                                          itemCount: marketList.length,
                                          separatorBuilder: (context, index) => 20.verticalSpace,
                                          itemBuilder: (context, i) {
                                            return (marketList.length - 1) == i
                                                ? VisibilityDetector(
                                                    key: Key(i.toString()),
                                                    onVisibilityChanged: (VisibilityInfo info) {
                                                      if (!stop &&
                                                          i == (marketList.length - 1) &&
                                                          !isLoading.value &&
                                                          !isPageLoading.value) {
                                                        getStoreList(isFirst: false);
                                                      }
                                                    },
                                                    child: Column(
                                                      children: [
                                                        storeItemVewMarketTab(i, marketList.length - 1 == i),
                                                        ValueListenableBuilder<bool>(
                                                          valueListenable: isPageLoading,
                                                          builder: (context, value, _) {
                                                            if (categoryModel?.pages != null) {
                                                              if (value && page <= categoryModel!.pages!) {
                                                                return UtilityForParty.progress();
                                                              }
                                                            }
                                                            return const SizedBox();
                                                          },
                                                        )
                                                      ],
                                                    ),
                                                  )
                                                : storeItemVewMarketTab(i, marketList.length - 1 == i);
                                          },
                                        ),
                                      SizedBox(
                                        height: 20,
                                      ),
                                    ],
                                  );
                                })
                          ],
                        ),
                      ),
                    ],
                  );
                }),
            // ValueListenableBuilder<bool>(
            //   valueListenable: isNewLoading,
            //   builder: (context, value, _) {
            //     if (value) {
            //       return LoadingScreen(text: '', image: groceryGif);
            //     } else {
            //       return SizedBox();
            //     }
            //   },
            // )
          ],
        ));
  }

  Widget noDatFound(BuildContext context) {
    return Container(
      width: double.infinity,
      height: MediaQuery.of(context).size.height * 0.6,
      child: Center(
        child: Container(
          width: 200,
          height: 50,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white,
            // ) , child: Image.asset(loadingbar, height: 35, width: 35)),
          ),
          child: Text(
            "No store available here",
            textAlign: TextAlign.center,
            style: TextStyle(
              color: grey_96a6a3,
              fontFamily: sf_pro_display_semibold,
              fontStyle: FontStyle.normal,
              fontSize: 16.sp,
            ),
          ),
        ),
      ),
    );
  }

  Future alertBox(BuildContext context, MarketList marketList) {
    return showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.r)),
          insetPadding: EdgeInsets.all(25.r),
          clipBehavior: Clip.antiAliasWithSaveLayer,
          child: Container(
            width: double.infinity,
            height: 180.h,
            margin: EdgeInsets.only(left: 23.w, right: 23.w, bottom: 30.h, top: 30.h),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                // Alert!
                Text("Alert!",
                    style: TextStyle(
                        color: Color(0xfffa586a),
                        fontWeight: FontWeight.w700,
                        fontFamily: sf_pro_display_bold,
                        fontStyle: FontStyle.normal,
                        fontSize: 18.sp),
                    textAlign: TextAlign.left),

                SizedBox(
                  height: 18.h,
                ),

                // Your delivery address is international. Do you want to see only overseas products?
                Text("Your delivery address is international.Do you want to see only overseas products?",
                    style: TextStyle(
                        color: black_273433,
                        fontWeight: FontWeight.w400,
                        fontFamily: sf_pro_display_regular,
                        fontStyle: FontStyle.normal,
                        fontSize: 16.sp),
                    textAlign: TextAlign.left),

                SizedBox(
                  height: 34.h,
                ),

                Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: InkWell(
                        onTap: () {
                          Get.back();
                          Get.to(StoreDetailP2(marketList: marketList, overseas: 0));
                        },
                        child: Container(
                          decoration: BoxDecoration(
                              border: Border.all(width: 1, color: green_24D39E),
                              borderRadius: BorderRadius.circular(8.r),
                              color: Colors.white),
                          child: // CONTINUE
                              Padding(
                            padding: EdgeInsets.all(13.5),
                            child: Text("No",
                                style: TextStyle(
                                    color: green_24D39E,
                                    fontWeight: FontWeight.w700,
                                    fontFamily: sf_pro_display_bold,
                                    fontStyle: FontStyle.normal,
                                    fontSize: 16.sp),
                                textAlign: TextAlign.center),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 16.w,
                    ),
                    Expanded(
                      flex: 1,
                      child: InkWell(
                        onTap: () {
                          Get.back();

                          Get.to(StoreDetailP2(marketList: marketList, overseas: 1));
                        },
                        child: Container(
                          decoration: BoxDecoration(
                              border: Border.all(width: 1, color: green_24D39E),
                              borderRadius: BorderRadius.circular(8.r),
                              color: Colors.white),
                          child: // CONTINUE
                              Padding(
                            padding: EdgeInsets.all(13.5),
                            child: Text("Yes",
                                style: TextStyle(
                                    color: green_24D39E,
                                    fontWeight: FontWeight.w700,
                                    fontFamily: sf_pro_display_bold,
                                    fontStyle: FontStyle.normal,
                                    fontSize: 16.sp),
                                textAlign: TextAlign.center),
                          ),
                        ),
                      ),
                    )
                  ],
                )
              ],
            ),
          ),
        );
      },
    );
  }

  switchLocationBottomSheet() {
    showModalBottomSheet(
      useSafeArea: true,
      isScrollControlled: true,
      context: context,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(30.0),
          topRight: Radius.circular(30.0),
        ),
      ),
      builder: (BuildContext context) {
        return Wrap(
          children: [
            Obx(() {
              return Container(
                height: 260.h,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(15.0),
                    topRight: Radius.circular(15.0),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          children: [
                            Text(
                              "SWITCH LOCATION",
                              style: AppTextStyle.xSmallMedium,
                            ),
                            Spacer(),
                            InkWell(
                              onTap: () {
                                Get.back();
                              },
                              child: Container(
                                height: 30.w,
                                width: 30.w,
                                decoration: BoxDecoration(
                                  color: grey_E9ECEC,
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.close,
                                  size: 15,
                                  color: black_273433,
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                      SizedBox(height: 7.w),
                      Container(
                        decoration: BoxDecoration(
                          color: grey_f3f6f6,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                          child: InkWell(
                            onTap: () async {
                              marketController.searchCountryText.value.clear();
                              displayCountryBottomSheet(context);
                            },
                            child: rowSelectTextIcon(
                              rightArrow: true,
                              title: marketController.selectCountry.value.id == null
                                  ? "Select Country"
                                  : marketController.selectCountry.value.name.toString(),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 10),
                      Row(
                        children: [
                          Flexible(
                            child: InkWell(
                              onTap: marketController.selectCountry.value.id == null
                                  ? () {
                                      showSnackBar(
                                        message: "Please select country",
                                      );
                                    }
                                  : () {
                                      marketController.searchCountryText.value.clear();
                                      displayStateBottomSheet(context);
                                    },
                              child: Container(
                                decoration: BoxDecoration(color: grey_f3f6f6, borderRadius: BorderRadius.circular(8)),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                                  child: rowSelectTextIcon(
                                    rightArrow: true,
                                    title: marketController.selectState.value.id == null
                                        ? "Select State"
                                        : marketController.selectState.value.name == null
                                            ? "Select State"
                                            : marketController.selectState.value.name.toString(),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: 10),
                          Flexible(
                            child: InkWell(
                              onTap: marketController.selectState.value.name == null
                                  ? () {
                                      showSnackBar(
                                        message: "Please select state",
                                      );
                                    }
                                  : () async {
                                      marketController.searchCountryText.value.clear();

                                      displayCityBottomSheet(
                                        context,
                                        screenName: "Map",
                                      );
                                      // var preferences = MySharedPref();
                                      // SigninModel? myModel = await preferences.getSignInModel(
                                      //   SharePreData.key_SaveSignInModel,
                                      // );
                                    },
                              child: Container(
                                decoration: BoxDecoration(
                                  color: grey_f3f6f6,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                                  child: rowSelectTextIcon(
                                    rightArrow: true,
                                    title: marketController.selectCity.value.id == null
                                        ? "Select City"
                                        : marketController.selectCity.value.name.toString(),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 10),
                      Flexible(
                        child: InkWell(
                          onTap: marketController.selectCity.value.id == null
                              ? () {
                                  showSnackBar(
                                    message: "Please select city",
                                  );
                                }
                              : () {
                                  Get.back();
                                  marketController.searchCountryText.value.clear();
                                  // Get.to(AddLocationByMap());
                                  Get.to(
                                    () => SearchRecommendStoreP3(
                                      cityId: marketController.selectCity.value.id.toString(),
                                      countryId: marketController.selectCountry.value.id.toString(),
                                      stateId: marketController.selectState.value.id.toString(),
                                      categoryName: marketController.selectCity.value.name.toString(),
                                    ),
                                  );
                                },
                          child: Container(
                            height: 48.h,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.black,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                              ),
                              child: Center(
                                child: Text(
                                  "View Store",
                                  style: AppTextStyle.mediumMedium.copyWith(color: Colors.white),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            })
          ],
        );
      },
    );
  }

  Widget rowSelectTextIcon({
    String? title,
    bool viewIcon = false,
    bool rightArrow = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (viewIcon == true)
          Icon(
            Icons.my_location_sharp,
            color: Color(0xff24D39E),
            size: 20,
          ),
        Flexible(
          child: Text(
            title!,
            overflow: TextOverflow.ellipsis,
            softWrap: true,
            style: AppTextStyle.smallMedium.copyWith(color: grey_600),
          ),
        ),
        viewIcon == true ? Spacer() : SizedBox(),
        if (rightArrow == true)
          Icon(
            Icons.arrow_forward_ios_rounded,
            color: black_273433,
            size: 16,
          )
      ],
    );
  }

  void displayCountryBottomSheet(BuildContext context) async {
    // calling funtion to handle search country field

    showModalBottomSheet<void>(
      isScrollControlled: true,
      context: context,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(topLeft: Radius.circular(30.0), topRight: Radius.circular(30.0)),
      ),
      builder: (BuildContext context) {
        SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.white, // navigation bar color
          statusBarColor: blue_3551ff, // status bar color
          statusBarIconBrightness: Brightness.light, // status bar icons' color
          systemNavigationBarIconBrightness: Brightness.light, //navigation bar icons' color
        ));

        return DraggableScrollableSheet(
            initialChildSize: 0.8,
            //set this as you want
            maxChildSize: 1,
            //set this as you want
            minChildSize: 0.1,
            //set this as you want
            expand: true,
            builder: (context, scrollController) {
              return StatefulBuilder(
                builder: (BuildContext context, StateSetter setState /*You can rename this!*/) {
                  return Obx(
                    () => Padding(
                        padding: MediaQuery.of(context).viewInsets,
                        child: Container(
                            height: MediaQuery.of(context).size.height - 500.0,
                            decoration: new BoxDecoration(
                              color: Colors.white,
                              borderRadius: new BorderRadius.only(
                                topLeft: const Radius.circular(30.0),
                                topRight: const Radius.circular(30.0),
                              ),
                            ),
                            child: Column(
                              children: [
                                SizedBox(
                                  height: 16,
                                ),
                                // Rectangle 1329
                                Align(
                                  alignment: Alignment.topCenter,
                                  child: Opacity(
                                    opacity: 0.4000000059604645,
                                    child: Container(
                                      width: 48,
                                      height: 4,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.all(Radius.circular(4)),
                                        color: const Color(0xff96a6a3),
                                      ),
                                    ),
                                  ),
                                ),

                                /*Search Layout Below Side*/
                                Padding(
                                  padding: const EdgeInsets.only(left: 24, right: 24, top: 20, bottom: 16),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(width: 1, color: grey_e9ecec),
                                      color: Colors.white,
                                    ),
                                    child: Padding(
                                      padding: EdgeInsets.only(left: 15, right: 15, top: 4.5, bottom: 4.5),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          SvgPicture.asset(
                                            search,
                                            width: 18,
                                            height: 18,
                                            color: black_273433,
                                          ),
                                          SizedBox(
                                            width: 7.7,
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Padding(
                                              padding: const EdgeInsets.all(5),
                                              child: new TextField(
                                                controller: marketController.searchCountryText.value,
                                                onChanged: (value) async {
                                                  await marketController.callCountryListApi(
                                                      marketController.searchCountryText.value.text);

                                                  setState(() {});
                                                },
                                                style: TextStyle(
                                                    color: black,
                                                    fontWeight: FontWeight.w500,
                                                    fontFamily: SF_PRO_DISPLAY_MEDIUM,
                                                    fontStyle: FontStyle.normal,
                                                    fontSize: 15.0),
                                                decoration: new InputDecoration(
                                                  isDense: true,
                                                  contentPadding: EdgeInsets.symmetric(horizontal: 0, vertical: 10),
                                                  hintText: "Search Country",
                                                  hintStyle: AppTextStyle.mediumRegular.copyWith(color: grey_500),
                                                  border: InputBorder.none,
                                                ),
                                                keyboardType: TextInputType.text,
                                                cursorColor: black,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),

                                SizedBox(
                                  height: 20,
                                ),

                                Expanded(
                                  child: ListView.separated(
                                    scrollDirection: Axis.vertical,
                                    primary: false,
                                    shrinkWrap: true,
                                    separatorBuilder: (context, index) => SizedBox(
                                      height: 0,
                                    ),
                                    padding: EdgeInsets.only(top: 0, bottom: 0, right: 24, left: 12),
                                    itemCount: marketController.countrylist.length,
                                    // itemCount: controller.listToShow.length,
                                    itemBuilder: (_, i) {
                                      // var noun = controller.listToShow[i];

                                      return InkWell(
                                        onTap: () async {
                                          marketController.selectCountry.value = marketController.countrylist[i];
                                          //marketList.clear();
                                          await marketController.displayMarketCountryList(
                                            "",
                                            isFilterData: true,
                                            CountryId: marketController.selectCountry.value.id.toString(),
                                          );

                                          Navigator.pop(context);
                                          marketController.selectState.value = StateModel();
                                          marketController.selectCity.value = CityDatum();
                                          await marketController.callStateListApi("");
                                        },
                                        child: Padding(
                                          padding: const EdgeInsets.only(left: 16, right: 24, bottom: 34),
                                          child: Row(
                                            children: [
                                              if (marketController.countrylist[i].name != null)
                                                //   SizedBox(
                                                //     height: 20,
                                                //     width: 28,
                                                //     child: marketController.countrylist[i].countryLogo != null
                                                //         ? ClipRRect(
                                                //             borderRadius: BorderRadius.circular(5),
                                                //             child: CachedNetworkImage(
                                                //               key: UniqueKey(),
                                                //               imageUrl:
                                                //                   marketController.countrylist[i].countryLogo.toString(),
                                                //               height: 20,
                                                //               fit: BoxFit.cover,
                                                //               placeholder: (context, url) => SvgPicture.asset(
                                                //                 add_placeholder,
                                                //                 height: 20,
                                                //               ),
                                                //               errorWidget: (context, url, error) => SvgPicture.asset(
                                                //                 add_placeholder,
                                                //                 height: 20,
                                                //               ),
                                                //             ),
                                                //           )
                                                //         : SvgPicture.asset(
                                                //             add_placeholder,
                                                //             width: 28,
                                                //             height: 20,
                                                //           ),
                                                //   ),
                                                // SizedBox(
                                                //   width: 16,
                                                // ),
                                                if (marketController.countrylist[i].name != null)
                                                  Text(
                                                    marketController.countrylist[i].name.toString(),
                                                    style: AppTextStyle.mediumMedium,
                                                  ),
                                              SizedBox(
                                                width: 10,
                                              ),
                                              // if (marketController
                                              //         .countrylist[i]
                                              //         .countryCode !=
                                              //     null)
                                              //   Text(
                                              //     marketController
                                              //             .countrylist[i]
                                              //             .countryCode!
                                              //             .isNotEmpty
                                              //         ? "(${marketController.countrylist[i].countryCode})"
                                              //         : "",
                                              //     style: TextStyle(
                                              //       fontFamily:
                                              //           sf_pro_display_regular,
                                              //       fontSize: 14,
                                              //     ),
                                              //   ),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                )
                              ],
                            ))),
                  );
                },
              );
            });
      },
    );
  }

  Widget liveStoreItem(MarketList store) {
    return LiveStoreItemView(
      distanceValue: double.tryParse(store.distance?.toString() ?? '') ?? 0,
      isLive: 1,
      address: store.address,
      onTap: () {
        final distanceValue = double.tryParse(store.distance?.toString() ?? '') ?? 0;

        Get.to(
          StoreDetailUi(
            storeName: store.name,
            overseas: 0,
            storeId: store.id.toString(),
            isStoreClose: store.storeStatus == 'Closed',
            storeStautsText: store.storeStatusText ?? '',
            isStoreoutOfRange: distanceValue > 20,
          ),
        )!
            .then((value) async {
          await homeController.getCartUpdate();
        });
      },
      storeName: store.name,
      storeImage: store.cardImage ?? store.image,
      storeStatus: store.storeStatus,
    );
  }

  storeItemVew(int index, bool isLast) {
    log('### index $index');
    log('### index-2 ${favStoreList.value.length}');
    final store = favStoreList.value[index];
    return InkWell(
      onTap: () {
        final distanceValue = double.tryParse(store.distance?.toString() ?? '') ?? 0;

        Get.to(StoreDetailUi(
          storeName: store.name ?? '',
          storeStautsText: store.storeStatus ?? '',
          overseas: 0,
          storeId: store.id?.toString() ?? '',
          isStoreClose: store.storeStatus == 'Closed',
          isStoreoutOfRange: distanceValue > 20,
        ))?.then((value) async {
          await homeController.getCartUpdate();
        });
      },
      child: StoreCardView(
        dontShowShadow: true,
        margin: EdgeInsets.fromLTRB(16.w, 0, 16.w, 0.h),
        storeImage: store.image,
        storeName: store.name,
        storeCityName: '${store.street}, ${store.cityDetails?.name}',
        storeStatus: store.storeStatus,
        storeStatusText: store.storeStatusText,
        distance: store.distance?.toStringAsFixed(1),
        isLast: isLast,
      ),
    );
  }

  storeItemVewMarketTab(int index, bool isLast) {
    final store = marketList[index];
    return InkWell(
      onTap: () {
        final distanceValue = double.tryParse(store.distance?.toString() ?? '') ?? 0;

        Get.to(StoreDetailUi(
          storeName: store.name ?? '',
          storeStautsText: store.storeStatus ?? '',
          overseas: 0,
          storeId: store.id?.toString() ?? '',
          isStoreClose: store.storeStatus == 'Closed',
          isStoreoutOfRange: distanceValue > 20,
        ))?.then((value) async {
          await homeController.getCartUpdate();
        });
      },
      child: StoreCardView(
        dontShowShadow: true,
        margin: EdgeInsets.fromLTRB(16.w, 0, 16.w, 0.h),
        storeImage: store.image,
        storeName: store.name,
        storeCityName: '${store.street}, ${store.cityDetails?.name}',
        storeStatus: store.storeStatus,
        storeStatusText: store.storeStatusText,
        distance: store.distance?.toStringAsFixed(1),
        isLast: isLast,
      ),
    );
  }

  void displayStateBottomSheet(BuildContext context) async {
    // calling funtion to handle search country field

    showModalBottomSheet<void>(
      isScrollControlled: true,
      context: context,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(topLeft: Radius.circular(30.0), topRight: Radius.circular(30.0)),
      ),
      builder: (BuildContext context) {
        SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.white, // navigation bar color
          statusBarColor: blue_3551ff, // status bar color
          statusBarIconBrightness: Brightness.light, // status bar icons' color
          systemNavigationBarIconBrightness: Brightness.light, //navigation bar icons' color
        ));

        return DraggableScrollableSheet(
            initialChildSize: 0.8,
            //set this as you want
            maxChildSize: 1,
            //set this as you want
            minChildSize: 0.1,
            //set this as you want
            expand: true,
            builder: (context, scrollController) {
              return StatefulBuilder(
                builder: (BuildContext context, StateSetter setState /*You can rename this!*/) {
                  return Obx(
                    () => Padding(
                        padding: MediaQuery.of(context).viewInsets,
                        child: Container(
                            height: MediaQuery.of(context).size.height - 500.0,
                            decoration: new BoxDecoration(
                                color: Colors.white,
                                borderRadius: new BorderRadius.only(
                                    topLeft: const Radius.circular(30.0), topRight: const Radius.circular(30.0))),
                            child: Column(
                              children: [
                                SizedBox(
                                  height: 16,
                                ),
                                // Rectangle 1329
                                Align(
                                  alignment: Alignment.topCenter,
                                  child: Opacity(
                                    opacity: 0.4000000059604645,
                                    child: Container(
                                        width: 48,
                                        height: 4,
                                        decoration: BoxDecoration(
                                            borderRadius: BorderRadius.all(Radius.circular(4)),
                                            color: const Color(0xff96a6a3))),
                                  ),
                                ),

                                /*Search Layout Below Side*/
                                Padding(
                                  padding: const EdgeInsets.only(left: 24, right: 24, top: 20, bottom: 16),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(width: 1, color: grey_e9ecec),
                                      color: Colors.white,
                                    ),
                                    child: Padding(
                                      padding: EdgeInsets.only(left: 15, right: 15, top: 4.5, bottom: 4.5),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          SvgPicture.asset(
                                            search,
                                            width: 18,
                                            height: 18,
                                            color: black_273433,
                                          ),
                                          SizedBox(
                                            width: 7.7,
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Padding(
                                              padding: const EdgeInsets.all(5),
                                              child: new TextField(
                                                controller: marketController.searchCountryText.value,
                                                onChanged: (value) async {
                                                  await marketController
                                                      .callStateListApi(marketController.searchCountryText.value.text);
                                                  // await marketController
                                                  //     .filterCountryList(
                                                  //         value);

                                                  setState(() {});
                                                },
                                                style: TextStyle(
                                                    color: black,
                                                    fontWeight: FontWeight.w500,
                                                    fontFamily: SF_PRO_DISPLAY_MEDIUM,
                                                    fontStyle: FontStyle.normal,
                                                    fontSize: 15.0),
                                                decoration: new InputDecoration(
                                                  isDense: true,
                                                  contentPadding: EdgeInsets.symmetric(horizontal: 0, vertical: 10),
                                                  hintText: "Search State",
                                                  hintStyle: TextStyle(
                                                      color: grey_96a6a3,
                                                      fontWeight: FontWeight.w500,
                                                      fontFamily: SF_PRO_DISPLAY_MEDIUM,
                                                      fontStyle: FontStyle.normal,
                                                      fontSize: 16.0),
                                                  border: InputBorder.none,
                                                ),
                                                keyboardType: TextInputType.text,
                                                cursorColor: black,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),

                                SizedBox(
                                  height: 20,
                                ),

                                Expanded(
                                  child: ListView.separated(
                                    scrollDirection: Axis.vertical,
                                    primary: false,
                                    shrinkWrap: true,
                                    separatorBuilder: (context, index) => SizedBox(
                                      height: 0,
                                    ),
                                    padding: EdgeInsets.only(top: 0, bottom: 0, right: 24, left: 12),
                                    itemCount: marketController.stateList.length,
                                    // itemCount: controller.listToShow.length,
                                    itemBuilder: (_, i) {
                                      // var noun = controller.listToShow[i];
                                      return InkWell(
                                        onTap: () async {
                                          marketController.selectState.value = marketController.stateList[i];

                                          marketController.selectStateData();

                                          Navigator.pop(context);
                                          marketController.selectCity.value = CityDatum();

                                          await marketController.callCityListApi("");
                                        },
                                        child: Padding(
                                          padding: const EdgeInsets.only(left: 16, right: 24, bottom: 34),
                                          child: Row(
                                            children: [
                                              // SizedBox(
                                              //   height: 20,
                                              //   width: 28,
                                              //   child: marketController
                                              //               .stateList[i]
                                              //               . !=
                                              //           null
                                              //       ? ClipRRect(
                                              //           borderRadius:
                                              //               BorderRadius
                                              //                   .circular(
                                              //                       5),
                                              //           child:
                                              //               CachedNetworkImage(
                                              //             key:
                                              //                 UniqueKey(),
                                              //             imageUrl: marketController
                                              //                 .countrylist[
                                              //                     i]
                                              //                 .countryLogo
                                              //                 .toString(),
                                              //             height: 20,
                                              //             fit:
                                              //                 BoxFit.cover,
                                              //             placeholder: (context,
                                              //                     url) =>
                                              //                 SvgPicture
                                              //                     .asset(
                                              //               add_placeholder,
                                              //               height: 20,
                                              //             ),
                                              //             errorWidget: (context,
                                              //                     url,
                                              //                     error) =>
                                              //                 SvgPicture
                                              //                     .asset(
                                              //               add_placeholder,
                                              //               height: 20,
                                              //             ),
                                              //           ),
                                              //         )
                                              //       : SvgPicture.asset(
                                              //           add_placeholder,
                                              //           width: 28,
                                              //           height: 20,
                                              //         ),
                                              // ),
                                              // SizedBox(
                                              //   width: 16,
                                              // ),
                                              Text(
                                                marketController.stateList[i].name.toString(),
                                                style: TextStyle(
                                                  fontFamily: SF_PRO_DISPLAY_MEDIUM,
                                                  fontSize: 16,
                                                ),
                                              ),
                                              SizedBox(
                                                width: 10,
                                              ),
                                              // Text(
                                              //   marketController
                                              //           .stateList[i]
                                              //           .countryCode!
                                              //           .isNotEmpty
                                              //       ? "(${marketController.countrylist[i].countryCode})"
                                              //       : "",
                                              //   style: TextStyle(
                                              //     fontFamily:
                                              //         sf_pro_display_regular,
                                              //     fontSize: 14,
                                              //   ),
                                              // ),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                )
                              ],
                            ))),
                  );
                },
              );
            });
      },
    );
  }

  void displayCityBottomSheet(
    BuildContext context, {
    String? screenName,
  }) async {
    // calling funtion to handle search country field

    showModalBottomSheet<void>(
      isScrollControlled: true,
      context: context,
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(topLeft: Radius.circular(30.0), topRight: Radius.circular(30.0)),
      ),
      builder: (BuildContext context) {
        SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.white, // navigation bar color
          statusBarColor: blue_3551ff, // status bar color
          statusBarIconBrightness: Brightness.light, // status bar icons' color
          systemNavigationBarIconBrightness: Brightness.light, //navigation bar icons' color
        ));

        return DraggableScrollableSheet(
            initialChildSize: 0.8,
            //set this as you want
            maxChildSize: 1,
            //set this as you want
            minChildSize: 0.1,
            //set this as you want
            expand: true,
            builder: (context, scrollController) {
              return StatefulBuilder(
                builder: (BuildContext context, StateSetter setState /*You can rename this!*/) {
                  return Obx(
                    () => Padding(
                        padding: MediaQuery.of(context).viewInsets,
                        child: Container(
                            height: MediaQuery.of(context).size.height - 500.0,
                            decoration: new BoxDecoration(
                                color: Colors.white,
                                borderRadius: new BorderRadius.only(
                                    topLeft: const Radius.circular(30.0), topRight: const Radius.circular(30.0))),
                            child: Column(
                              children: [
                                SizedBox(
                                  height: 16,
                                ),
                                // Rectangle 1329
                                Align(
                                  alignment: Alignment.topCenter,
                                  child: Opacity(
                                    opacity: 0.4000000059604645,
                                    child: Container(
                                        width: 48,
                                        height: 4,
                                        decoration: BoxDecoration(
                                            borderRadius: BorderRadius.all(Radius.circular(4)),
                                            color: const Color(0xff96a6a3))),
                                  ),
                                ),

                                /*Search Layout Below Side*/
                                Padding(
                                  padding: const EdgeInsets.only(left: 24, right: 24, top: 20, bottom: 16),
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(width: 1, color: grey_e9ecec),
                                      color: Colors.white,
                                    ),
                                    child: Padding(
                                      padding: EdgeInsets.only(left: 15, right: 15, top: 4.5, bottom: 4.5),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          SvgPicture.asset(
                                            search,
                                            width: 18,
                                            height: 18,
                                            color: black_273433,
                                          ),
                                          SizedBox(
                                            width: 7.7,
                                          ),
                                          Expanded(
                                            flex: 1,
                                            child: Padding(
                                              padding: const EdgeInsets.all(5),
                                              child: new TextField(
                                                controller: marketController.searchCountryText.value,
                                                onChanged: (value) async {
                                                  await marketController.callCityListApi(value);
                                                  setState(() {});
                                                  // await marketController
                                                  //     .filterCountryList(
                                                  //         value);
                                                },
                                                style: TextStyle(
                                                    color: black,
                                                    fontWeight: FontWeight.w500,
                                                    fontFamily: SF_PRO_DISPLAY_MEDIUM,
                                                    fontStyle: FontStyle.normal,
                                                    fontSize: 15.0),
                                                decoration: new InputDecoration(
                                                  isDense: true,
                                                  contentPadding: EdgeInsets.symmetric(horizontal: 0, vertical: 10),
                                                  hintText: "Search City",
                                                  hintStyle: TextStyle(
                                                      color: grey_96a6a3,
                                                      fontWeight: FontWeight.w500,
                                                      fontFamily: SF_PRO_DISPLAY_MEDIUM,
                                                      fontStyle: FontStyle.normal,
                                                      fontSize: 16.0),
                                                  border: InputBorder.none,
                                                ),
                                                keyboardType: TextInputType.text,
                                                cursorColor: black,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),

                                SizedBox(
                                  height: 20,
                                ),

                                Expanded(
                                  child: ListView.separated(
                                    scrollDirection: Axis.vertical,
                                    primary: false,
                                    shrinkWrap: true,
                                    separatorBuilder: (context, index) => SizedBox(height: 0),
                                    padding: EdgeInsets.only(top: 0, bottom: 0, right: 24, left: 12),
                                    itemCount: marketController.cityList.length,
                                    // itemCount: controller.listToShow.length,
                                    itemBuilder: (_, i) {
                                      // var noun = controller.listToShow[i];
                                      return InkWell(
                                        onTap: () async {
                                          marketController.selectCityId.value = marketController.cityList[i].id!;
                                          marketController.selectCity.value = marketController.cityList[i];
                                          marketController.displayMarketList(
                                            "",
                                            isFilter: true,
                                          );

                                          // marketController
                                          //     .selectCityData(context);

                                          if (mounted) {
                                            Navigator.pop(context);
                                          }
                                        },
                                        child: Padding(
                                          padding: const EdgeInsets.only(left: 16, right: 24, bottom: 34),
                                          child: Row(
                                            children: [
                                              Text(
                                                marketController.cityList[i].name.toString(),
                                                style: TextStyle(
                                                  fontFamily: SF_PRO_DISPLAY_MEDIUM,
                                                  fontSize: 16,
                                                ),
                                              ),
                                              SizedBox(
                                                width: 10,
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ))),
                  );
                },
              );
            });
      },
    );
  }

  // unauthorized user will notify of login by this dialog
  onWarningOfUnAuthorizedUser(BuildContext context) {
    return showDialog(
      barrierColor: Colors.black12.withOpacity(0.5),
      barrierDismissible: true,
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
          insetPadding: EdgeInsets.all(25),
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.all(25),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Hello Guest.. !!",
                  style: TextStyle(fontFamily: sf_pro_display_bold, color: pink_fa586a, fontSize: 18.sp),
                ),
                SizedBox(
                  height: 18.h,
                ),
                Text(
                  "Login is required to access additional app content. Please log in to proceed and enjoy a seamless shopping experience.",
                  style: TextStyle(fontFamily: sf_pro_display_regular, color: black_273433, fontSize: 16.sp),
                ),
                SizedBox(
                  height: 34.h,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      flex: 1,
                      child: InkWell(
                        onTap: () {
                          Navigator.of(context).pop(false); //Will not exit the App
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 14.h),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(
                              color: pink_d03784,
                              width: 1,
                            ),
                          ),
                          child: Text("Not Now",
                              style: TextStyle(fontSize: 14.sp, fontFamily: sf_pro_display_regular, color: pink_d03784),
                              textAlign: TextAlign.center),
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 15.w,
                    ),
                    Expanded(
                      flex: 1,
                      child: InkWell(
                        onTap: () {
                          Get.offAll(Welcome());
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 14.h),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(
                              color: pink_d03784,
                              width: 1,
                            ),
                          ),
                          child: Text(
                            "Login",
                            style: TextStyle(fontSize: 14.sp, fontFamily: sf_pro_display_regular, color: pink_d03784),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        );
        // WillPopScope(
        //   onWillPop: (){},
        //   child: alert);
      },
    );
  }

  Widget categoryView({String? image, String? title, VoidCallback? onTap}) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.only(right: 15.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 62.h,
              width: 62.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
              ),
              child: UtilityForParty.imageLoader(
                url: image ?? '',
                placeholder: grey_bg,
                isShapeCircular: true,
                shape: BoxShape.circle,
              ),
            ),
            SizedBox(height: 7.h),
            Text(
              title ?? '',
              overflow: TextOverflow.ellipsis,
              style: AppTextStyle.xSmallMedium,
            )
          ],
        ),
      ),
    );
  }

  void openAddressBottomSheet(BuildContext context, RestaurantListController controller) {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        isScrollControlled: true,
        isDismissible: false,
        enableDrag: false,
        builder: (context) {
          return addressBottomSheetContent(restListController: controller);
        }).then((value) {
      log('message2');
      page = 0;
      livePage = 0;
      marketList.clear();
      liveStoreList.clear();

      // Show delivery time bottom sheet after a short delay for first-time users
      if (PreferenceManagerUtils.getIsFirstTimeUser() &&
          !PreferenceManagerUtils.getHasSeenDeliveryTimeSheet() &&
          PreferenceManagerUtils.getYourAddress().isNotEmpty) {
        Future.delayed(Duration(milliseconds: 1500), () {
          if (mounted) {
            PreferenceManagerUtils.setHasSeenDeliveryTimeSheet(true);
            showDeliveryTimeBottomSheet(context);
          }
        });
      }

      init();
      setState(() {
        // initiatePagination();
      });
    });
  }

  // @override
  // void onReachedLast() {
  //   log('VVVVVVVVVVVVVVVV');
  //   if (!controller.stop.value && !controller.isRestuatrantPaginationLoading.value) {
  //     _debouncer.run(() {
  //       controller.callHomeAPI(isRestuarntNearYouPageLoading: true);
  //     });
  //   }
  // }

  Widget newAdvertishment() {
    return Stack(
      children: [
        Container(
          width: 280.w,
          height: 160.h,
          margin: EdgeInsets.fromLTRB(0, 16, 16, 0),
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          decoration: BoxDecoration(
            color: Color(0xffea6eb3).withOpacity(0.1),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Enjoy exclusive deals on spring\ncleaning, DIY & gardening items',
                style: AppTextStyle.mediumBold,
              ),
              const SizedBox(height: 8),
              Text(
                'T&C apply',
                style: AppTextStyle.xSmallMedium.copyWith(color: AppColors.grey),
              ),
              const SizedBox(height: 16),
              Container(
                decoration: BoxDecoration(
                  color: AppColors.white,
                  border: Border.all(color: AppColors.grey),
                  borderRadius: BorderRadius.circular(10),
                ),
                padding: EdgeInsets.symmetric(vertical: 2, horizontal: 5),
                child: Text(
                  'Order Now',
                  style: AppTextStyle.xSmallMedium,
                ),
              )
            ],
          ),
        ),
        Positioned(
          bottom: -25,
          right: 20,
          child: ClipRRect(
            borderRadius: const BorderRadius.only(topLeft: Radius.circular(100), topRight: Radius.circular(100)),
            // child: Container(
            //   color: Colors.red,
            //   height: 60,
            //   width: 100,
            // ),
            child: Image.asset(
              'assets/images/Vector.png', // replace with actual asset path
              width: 130,
              height: 75,
              fit: BoxFit.cover,
            ),
          ),
        )
      ],
    );
  }

  Widget newSportsOfWeek({FavStoreModel? store}) {
    final distanceValue = double.tryParse(store?.distance?.toString() ?? '') ?? 0;
    return InkWell(
      onTap: () {
        Get.to(
          StoreDetailUi(
            storeName: store?.name ?? '',
            storeStautsText: store?.storeStatus ?? '',
            // marketList: store,
            overseas: 0,
            storeId: store?.id.toString() ?? '',
            isStoreClose: store?.storeStatus == 'Closed',
            isStoreoutOfRange: distanceValue > 20,
          ),
        )!
            .then((value) async {
          await homeController.getCartUpdate();
        });
      },
      child: Container(
        width: 240.w,
        margin: EdgeInsets.only(
          right: 10,
          top: 18,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: CachedNetworkImage(
                key: UniqueKey(),
                width: 240.w,
                height: 140.h,
                imageUrl: store?.cardImage ?? store?.image ?? '',
                fit: BoxFit.cover,
                placeholder: (context, url) => Image.asset(
                  grey_bg,
                  width: 240.w,
                  height: 140.h,
                  fit: BoxFit.fill,
                ),
                errorWidget: (context, url, error) => Image.asset(
                  grey_bg,
                  width: 240.w,
                  height: 140.h,
                  fit: BoxFit.fill,
                ),
              ),
            ),
            SizedBox(
              height: 7.h,
            ),
            Container(
              width: 240.w,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          store?.name ?? '',
                          style: AppTextStyle.smallMedium,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                        SizedBox(
                          height: 2.h,
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            AppAssetImage(deliveryFeeNewIcon, width: 14, height: 14),
                            SizedBox(
                              width: 4.w,
                            ),
                            Flexible(
                              child: Text(
                                "${store?.address ?? ''}".split(',').take(1).join(','),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                                style: AppTextStyle.xSmallRegular.copyWith(color: AppColors.textMuted),
                                textAlign: TextAlign.left,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 2.h,
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            AppAssetImage(deliveryTimeNewIcon, width: 14, height: 14),
                            SizedBox(
                              width: 4.w,
                            ),
                            Text(
                              '${(distanceValue < 20) ? '20-35+ mins' : '2hr+'}',
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              style: AppTextStyle.xSmallRegular.copyWith(color: AppColors.textMuted),
                              textAlign: TextAlign.left,
                            ),
                            SizedBox(width: 26),
                            // if (store?.isLive != 0)
                            // Text(
                            //   store?.storeStatus ?? '',
                            //   style: AppTextStyle.xSmallRegular.copyWith(
                            //     color: store?.storeStatus == "Open" ? AppColors.cloverGreen : AppColors.fireEngineRed,
                            //     fontWeight: FontWeight.w500,
                            //   ),
                            // )
                          ],
                        )
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        margin: EdgeInsets.only(left: 16.w),
                        decoration: BoxDecoration(borderRadius: BorderRadius.circular(4.r), color: grey_100),
                        padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                        child: Text(
                          "${distanceValue > 20 ? "Out of Range" : "${distanceValue.toStringAsFixed(1)} mi"}",
                          style: AppTextStyle.xSmallMedium.copyWith(fontSize: 14),
                        ),
                      ),
                      SizedBox(height: 16),
                      store?.isLive == 0
                          ? Container(
                              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4.r),
                                color: grey_100,
                              ),
                              child: Text('Coming Soon', style: AppTextStyle.xSmallMedium),
                            )
                          : Container(
                              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4.r),
                                color: store?.storeStatus == "Open"
                                    ? AppColors.cloverGreen.withValues(alpha: 0.1)
                                    : AppColors.fireEngineRed.withValues(alpha: 0.1),
                              ),
                              child: Text(
                                store?.storeStatus ?? '',
                                style: AppTextStyle.xSmallRegular.copyWith(
                                  color: store?.storeStatus == "Open" ? AppColors.cloverGreen : AppColors.fireEngineRed,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            )
                    ],
                  )
                ],
              ),
            ),
            // SizedBox(
            //   height: 10.h,
            // ),
          ],
        ),
      ),
    );
  }
}
