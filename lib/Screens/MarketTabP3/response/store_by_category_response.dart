import 'package:groceryboouser/Screens/MarketTabP3/model/store_by_category_model.dart';

class StoresByCategoryResponse {
  bool? success;
  int? statusCode;
  String? message;
  List<StoreByCategoryModel>? data;
  int? pages;

  StoresByCategoryResponse({this.success, this.statusCode, this.message, this.data, this.pages});

  StoresByCategoryResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    statusCode = json['status_code'];
    message = json['message'];
    if (json['data'] != null) {
      data = <StoreByCategoryModel>[];
      json['data'].forEach((v) {
        data!.add(new StoreByCategoryModel.fromJson(v));
      });
    }
    pages = json['pages'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['status_code'] = this.statusCode;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    data['pages'] = this.pages;
    return data;
  }
}
