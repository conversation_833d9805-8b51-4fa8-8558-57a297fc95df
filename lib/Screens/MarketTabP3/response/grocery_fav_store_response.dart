import 'package:groceryboouser/Screens/MarketTabP3/model/store_model.dart';

class GroceryFavStoreResponse {
  bool? success;
  int? statusCode;
  String? message;
  List<FavStoreModel>? data;

  GroceryFavStoreResponse({this.success, this.statusCode, this.message, this.data});

  GroceryFavStoreResponse.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    statusCode = json['status_code'];
    message = json['message'];
    if (json['data'] != null) {
      data = <FavStoreModel>[];
      json['data'].forEach((v) {
        data!.add(new FavStoreModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['status_code'] = this.statusCode;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
