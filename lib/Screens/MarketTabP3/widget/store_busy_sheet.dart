import 'package:flutter/material.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:url_launcher/url_launcher.dart';

class StoreBusySheet extends StatefulWidget {
  const StoreBusySheet({super.key, required this.storeName, required this.contactNo});
  final String storeName;
  final String contactNo;

  @override
  State<StoreBusySheet> createState() => _StoreBusySheetState();
}

class _StoreBusySheetState extends State<StoreBusySheet> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.redPurpul,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      '${widget.storeName} Order Pending*',
                      style: AppTextStyle.bodyLarge
                          .copyWith(color: AppColors.white, fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Icon(
                      Icons.cancel,
                      color: AppColors.white.withValues(alpha: 0.5),
                      size: 30,
                    ),
                  )
                ],
              ),
              SizedBox(height: 16),
              Text(
                'Stores sometimes get busy.',
                style: AppTextStyle.bodyLarge.copyWith(color: AppColors.white),
              ),
              SizedBox(height: 16),
              InkWell(
                onTap: () async {
                  final Uri phoneLaunchUri = Uri(
                    scheme: 'tel',
                    path: widget.contactNo,
                  );
                  await launchUrl(phoneLaunchUri);
                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Call the store',
                        style: AppTextStyle.bodyMedium
                            .copyWith(color: AppColors.redPurpul, fontWeight: FontWeight.w600, fontSize: 16),
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
          Positioned(
            bottom: 20,
            right: 20,
            child: InkWell(
              onTap: () async {
                final Uri phoneLaunchUri = Uri(
                  scheme: 'tel',
                  path: widget.contactNo,
                );
                await launchUrl(phoneLaunchUri);
              },
              child: Container(
                padding: EdgeInsets.all(14),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: AppColors.white,
                ),
                child: Icon(Icons.phone_enabled, color: AppColors.redPurpul),
              ),
            ),
          )
        ],
      ),
    );
  }
}
