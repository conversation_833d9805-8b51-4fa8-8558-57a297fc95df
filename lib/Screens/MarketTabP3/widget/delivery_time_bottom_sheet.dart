import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_icons.dart';
import 'package:groceryboouser/Widgets/app_asset_image.dart';

class DeliveryTimeBottomSheet extends StatelessWidget {
  final VoidCallback onGetStarted;
  final VoidCallback onClose;

  const DeliveryTimeBottomSheet({
    Key? key,
    required this.onGetStarted,
    required this.onClose,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return // Delivery time banner
        Container(
      margin: EdgeInsets.symmetric(horizontal: 0.w),
      padding: EdgeInsets.only(left: 33, top: 37, right: 33, bottom: 26),
      decoration: BoxDecoration(
        color: AppColors.bottomSheetPinkColor, // Light pink color
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(70),
        ),
      ),
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '20 - 30 min delivery on',
                style: AppTextStyle.xxLargeBold.copyWith(
                  color: AppColors.textColor,
                  fontSize: 25,
                ),
              ),
              Text(
                'your grocery orders',
                style: AppTextStyle.mediumBold.copyWith(
                  color: AppColors.checkboxSelectedColor, // Pink color
                  fontSize: 25,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                'Call or chat with the store by clicking\non the icons.',
                style: AppTextStyle.smallRegular.copyWith(
                  color: AppColors.textMuted,
                  fontSize: 14,
                ),
              ),
              SizedBox(height: 16.h),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  GestureDetector(
                    onTap: onGetStarted,
                    child: Container(
                      width: 107,
                      height: 48,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                        color: AppColors.rangoonGreen,
                        borderRadius: BorderRadius.all(Radius.circular(8.r)),
                      ),
                      child: Text('Get started',
                          style: AppTextStyle.xSmallMedium.copyWith(color: AppColors.white),
                          textAlign: TextAlign.center),
                    ),
                  ),
                ],
              ),
            ],
          ),
          Positioned(
            top: 0,
            right: 0,
            child: GestureDetector(
              onTap: onClose,
              child: AppAssetImage(
                cancelGroceryNewIcon,
                width: 31,
                height: 31,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
