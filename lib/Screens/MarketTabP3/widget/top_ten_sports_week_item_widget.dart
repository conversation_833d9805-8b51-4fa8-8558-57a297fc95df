import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:groceryboouser/Screens/MarketTabP3/model/random_product_model.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_colors.dart';
import 'package:groceryboouser/Styles/my_icons.dart';

class TopTenSportsWeekItemWidget extends StatelessWidget {
  const TopTenSportsWeekItemWidget({super.key, this.onTap, required this.product});
  final VoidCallback? onTap;
  final Products product;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 120,
        margin: EdgeInsets.only(right: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              alignment: Alignment.bottomRight,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: CachedNetworkImage(
                    key: UniqueKey(),
                    maxHeightDiskCache: 400,
                    maxWidthDiskCache: 400,
                    width: 120,
                    height: 120,
                    fit: BoxFit.cover,
                    imageUrl: product.image ?? '',
                    placeholder: (context, url) => Image.asset(
                      grey_bg,
                      width: 120,
                      height: 120,
                      fit: BoxFit.cover,
                    ),
                    errorWidget: (context, url, error) => Image.asset(
                      grey_bg,
                      width: 120,
                      height: 120,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.all(4.r),
                  padding: EdgeInsets.symmetric(vertical: 0.h, horizontal: 10.w),
                  width: 30.h,
                  height: 30.w,
                  decoration: BoxDecoration(boxShadow: [
                    BoxShadow(
                      color: Color(0x1E000000),
                      blurRadius: 16,
                      offset: Offset(0, 4),
                      spreadRadius: 0,
                    )
                  ], color: white, borderRadius: BorderRadius.circular(4.r)),
                  child: SizedBox(
                    height: 8.h,
                    width: 8.w,
                    child: SvgPicture.asset(
                      icon_new_plus,
                      color: Colors.black,
                      height: 8.h,
                      width: 8.w,
                    ),
                  ),
                )
              ],
            ),
            SizedBox(height: 11),
            Text(
              '\$${product.cost}${product.weightUnit != null ? '/${product.weightUnit}' : ''}',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: AppTextStyle.smallSemiBold,
            ),
            SizedBox(height: 6),
            Text('${product.name}\n',
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: AppTextStyle.xSmallRegular.copyWith(color: grey_600)),
            SizedBox(height: 8),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: ((product.stock ?? 0) > 0)
                    ? Color(0xff66DC7E).withValues(alpha: 0.1)
                    : AppColors.fireEngineRed.withValues(alpha: 0.1),
                border: Border.all(width: 1, color: Color(0xff27272A).withValues(alpha: 0.1)),
              ),
              child: Text(
                ((product.stock ?? 0) > 0) ? 'In Stock' : 'Out of Stock',
                style: AppTextStyle.xSmallMedium
                    .copyWith(color: ((product.stock ?? 0) > 0) ? Color(0xff33803F) : AppColors.fireEngineRed),
              ),
            )
          ],
        ),
      ),
    );
  }
}
