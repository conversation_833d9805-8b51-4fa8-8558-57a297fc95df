class OrderLateNotificationsModel {
  int? id;
  int? orderId;
  int? storeId;
  int? userId;
  bool? isRead;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  Store? store;

  OrderLateNotificationsModel(
      {this.id,
      this.orderId,
      this.storeId,
      this.userId,
      this.isRead,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.store});

  OrderLateNotificationsModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    orderId = json['order_id'];
    storeId = json['store_id'];
    userId = json['user_id'];
    isRead = json['is_read'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    deletedAt = json['deleted_at'];
    store = json['store'] != null ? new Store.fromJson(json['store']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['order_id'] = this.orderId;
    data['store_id'] = this.storeId;
    data['user_id'] = this.userId;
    data['is_read'] = this.isRead;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['deleted_at'] = this.deletedAt;
    if (this.store != null) {
      data['store'] = this.store!.toJson();
    }
    return data;
  }
}

class Store {
  int? id;
  int? userId;
  int? cityId;
  int? stateId;
  int? countryId;
  int? masterStoreId;
  String? name;
  String? email;
  String? contactNumber;
  String? address;
  String? latitude;
  String? longitude;
  Null businessType;
  int? isLive;
  String? blockNumber;
  String? street;
  String? landmark;
  String? pincode;
  String? commission;
  String? image;
  String? cardImage;
  Null banner;
  String? fileType;
  int? status;
  Null openTime;
  Null closeTime;
  String? localDeliveryCharge;
  String? internationalDeliveryCharge;
  String? storeTiming;
  int? isRecommendedMarket;
  String? createdAt;
  String? updatedAt;
  Null deletedAt;

  Store(
      {this.id,
      this.userId,
      this.cityId,
      this.stateId,
      this.countryId,
      this.masterStoreId,
      this.name,
      this.email,
      this.contactNumber,
      this.address,
      this.latitude,
      this.longitude,
      this.businessType,
      this.isLive,
      this.blockNumber,
      this.street,
      this.landmark,
      this.pincode,
      this.commission,
      this.image,
      this.cardImage,
      this.banner,
      this.fileType,
      this.status,
      this.openTime,
      this.closeTime,
      this.localDeliveryCharge,
      this.internationalDeliveryCharge,
      this.storeTiming,
      this.isRecommendedMarket,
      this.createdAt,
      this.updatedAt,
      this.deletedAt});

  Store.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    cityId = json['city_id'];
    stateId = json['state_id'];
    countryId = json['country_id'];
    masterStoreId = json['master_store_id'];
    name = json['name'];
    email = json['email'];
    contactNumber = json['contact_number'];
    address = json['address'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    businessType = json['business_type'];
    isLive = json['is_live'];
    blockNumber = json['block_number'];
    street = json['street'];
    landmark = json['landmark'];
    pincode = json['pincode'];
    commission = json['commission'];
    image = json['image'];
    cardImage = json['card_image'];
    banner = json['banner'];
    fileType = json['file_type'];
    status = json['status'];
    openTime = json['open_time'];
    closeTime = json['close_time'];
    localDeliveryCharge = json['local_delivery_charge'];
    internationalDeliveryCharge = json['international_delivery_charge'];
    storeTiming = json['store_timing'];
    isRecommendedMarket = json['is_recommended_market'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    deletedAt = json['deleted_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['user_id'] = this.userId;
    data['city_id'] = this.cityId;
    data['state_id'] = this.stateId;
    data['country_id'] = this.countryId;
    data['master_store_id'] = this.masterStoreId;
    data['name'] = this.name;
    data['email'] = this.email;
    data['contact_number'] = this.contactNumber;
    data['address'] = this.address;
    data['latitude'] = this.latitude;
    data['longitude'] = this.longitude;
    data['business_type'] = this.businessType;
    data['is_live'] = this.isLive;
    data['block_number'] = this.blockNumber;
    data['street'] = this.street;
    data['landmark'] = this.landmark;
    data['pincode'] = this.pincode;
    data['commission'] = this.commission;
    data['image'] = this.image;
    data['card_image'] = this.cardImage;
    data['banner'] = this.banner;
    data['file_type'] = this.fileType;
    data['status'] = this.status;
    data['open_time'] = this.openTime;
    data['close_time'] = this.closeTime;
    data['local_delivery_charge'] = this.localDeliveryCharge;
    data['international_delivery_charge'] = this.internationalDeliveryCharge;
    data['store_timing'] = this.storeTiming;
    data['is_recommended_market'] = this.isRecommendedMarket;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['deleted_at'] = this.deletedAt;
    return data;
  }
}
