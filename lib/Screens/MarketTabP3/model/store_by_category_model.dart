import 'package:groceryboouser/Screens/MarketTabP3/model/store_model.dart';

class StoreByCategoryModel {
  String? title;
  String? subTitle;
  int? categoryId;
  List<FavStoreModel>? stores;

  StoreByCategoryModel({this.title, this.categoryId, this.stores, this.subTitle});

  StoreByCategoryModel.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    subTitle = json['sub_title'];
    categoryId = json['id'];
    if (json['stores'] != null) {
      stores = <FavStoreModel>[];
      json['stores'].forEach((v) {
        stores!.add(new FavStoreModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['title'] = this.title;
    data['sub_title'] = this.subTitle;
    data['id'] = this.categoryId;
    if (this.stores != null) {
      data['stores'] = this.stores!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
