class Products {
  int? id;
  String? name;
  String? description;
  String? image;
  String? cost;
  String? usdCost;
  int? stock;
  String? weightUnit;
  Null basketProductValue;

  Products({
    this.id,
    this.name,
    this.description,
    this.image,
    this.cost,
    this.usdCost,
    this.stock,
    this.basketProductValue,
    this.weightUnit,
  });

  Products.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    image = json['image'];
    cost = json['cost'];
    usdCost = json['usd_cost'];
    stock = json['stock'];
    basketProductValue = json['basket_product_value'];
    weightUnit = json['weight_unit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['description'] = this.description;
    data['image'] = this.image;
    data['cost'] = this.cost;
    data['usd_cost'] = this.usdCost;
    data['stock'] = this.stock;
    data['basket_product_value'] = this.basketProductValue;
    data['weight_unit'] = this.weightUnit;
    return data;
  }
}
