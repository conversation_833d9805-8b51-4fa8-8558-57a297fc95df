import 'package:groceryboouser/Screens/MarketTabP3/model/random_product_model.dart';

class RandomStoreModel {
  int? id;
  String? name;
  String? image;
  String? address;
  String? latitude;
  String? longitude;
  String? stateName;
  String? cityName;
  String? storeStatus;
  String? storeStatusText;
  bool? checkAddress;
  double? distance;
  List<Products>? products;

  RandomStoreModel(
      {this.id,
      this.name,
      this.image,
      this.address,
      this.latitude,
      this.longitude,
      this.stateName,
      this.distance,
      this.cityName,
      this.storeStatus,
      this.storeStatusText,
      this.checkAddress,
      this.products});

  RandomStoreModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    image = json['image'];
    address = json['address'];
    latitude = json['latitude'];
    distance = json['distance'];
    longitude = json['longitude'];
    stateName = json['state_name'];
    cityName = json['city_name'];
    storeStatus = json['store_status'];
    storeStatusText = json['store_status_text'];
    checkAddress = json['check_address'];
    if (json['products'] != null) {
      products = <Products>[];
      json['products'].forEach((v) {
        products!.add(new Products.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['image'] = this.image;
    data['distance'] = this.distance;
    data['address'] = this.address;
    data['latitude'] = this.latitude;
    data['longitude'] = this.longitude;
    data['state_name'] = this.stateName;
    data['city_name'] = this.cityName;
    data['store_status'] = this.storeStatus;
    data['store_status_text'] = this.storeStatusText;
    data['check_address'] = this.checkAddress;
    if (this.products != null) {
      data['products'] = this.products!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
