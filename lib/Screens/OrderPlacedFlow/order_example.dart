import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:groceryboouser/Screens/OrderPlacedFlow/order_loading_page.dart';

class OrderExample extends StatelessWidget {
  const OrderExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Order Example'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            // Navigate to the order loading page
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => OrderLoadingPage(
                  address: '625 Piedmont Avenue, NE',
                  deliveryTime: 'Fast delivery: 35-60 mins',
                  storeName: 'Mawa Supermarket',
                  items: [
                    {
                      'name': 'Thomas\' Pre-Sliced Plain Bagels',
                      'quantity': '2',
                      'size': '(1.25 lb) 20 oz',
                    },
                  ],
                ),
              ),
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.black,
            padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          child: Text(
            'Place Order',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}
