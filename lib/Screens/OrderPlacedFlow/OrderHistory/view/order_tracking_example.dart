import 'package:flutter/material.dart';
import 'package:groceryboouser/Screens/OrderPlacedFlow/OrderHistory/view/order_tracking_page.dart';
import 'package:groceryboouser/Styles/app_colors.dart';

class OrderTrackingExample extends StatelessWidget {
  const OrderTrackingExample({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Order Tracking Example'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            // Navigate to the order tracking page
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const OrderTrackingPage(
                  orderId: 12345,
                ),
              ),
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          child: const Text(
            'Track Order',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}
