import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_icons.dart';

class OrderTimelineStepper extends StatelessWidget {
  final List<OrderTimelineStep> steps;
  final int currentStep;
  final bool isLoading;

  const OrderTimelineStepper({
    Key? key,
    required this.steps,
    required this.currentStep,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(0, 0, 0, 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Timeline',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 16),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: steps.length,
            itemBuilder: (context, index) {
              final bool isActive = index <= currentStep;
              final bool isCurrentStep = index == currentStep;
              final bool isLastStep = index == steps.length - 1;
              return Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Step indicator
                      Column(
                        children: [
                          _buildStepIndicator(isActive, isCurrentStep, isLoading),
                          if (!isLastStep) _buildDottedLine(isActive),
                        ],
                      ),
                      const SizedBox(width: 12),
                      // Step content
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              steps[index].title,
                              style: AppTextStyle.mediumMedium.copyWith(
                                color: isActive ? AppColors.textColor : AppColors.textMuted,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              steps[index].time,
                              style: AppTextStyle.mediumMedium.copyWith(
                                color: isActive ? AppColors.textMuted : Colors.grey[500],
                                fontSize: 16,
                                fontWeight: FontWeight.w400,
                              ),
                              // style: TextStyle(
                              //   fontSize: 16,
                              //   fontWeight: FontWeight.w400,
                              //   color: isActive ? AppColors.textMuted : Colors.grey[500],
                              // ),
                            ),
                            if (!isLastStep) const SizedBox(height: 20),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDottedLine(bool isActive) {
    return Container(
      height: 60,
      width: 24,
      child: CustomPaint(
        painter: _DottedLinePainter(
          color: AppColors.steeperDottedLineColor,
          dashHeight: 3,
          dashSpace: 3,
          strokeWidth: 2,
        ),
      ),
    );
  }

  Widget _buildStepIndicator(bool isActive, bool isCurrentStep, bool isLoading) {
    if (isActive) {
      // Completed step
      return Container(
        width: 24,
        height: 24,
        decoration: const BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
        ),
        child: SvgPicture.asset(
          'assets/images/right_click.svg',
          width: 24,
          height: 24,
        ),
      );
    }
    return Container(
      width: 24,
      height: 24,
      padding: const EdgeInsets.all(2),
      child: Image.asset(
        timerLoading,
        width: 24,
        height: 24,
      ),
    );
  }
}

class _DottedLinePainter extends CustomPainter {
  final Color color;
  final double dashHeight;
  final double dashSpace;
  final double strokeWidth;

  _DottedLinePainter({
    required this.color,
    required this.dashHeight,
    required this.dashSpace,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final centerX = size.width / 2;
    double startY = 0;

    while (startY < size.height) {
      // Draw a small dash
      canvas.drawLine(
        Offset(centerX, startY),
        Offset(centerX, startY + dashHeight),
        paint,
      );

      // Move to the next dash position
      startY += dashHeight + dashSpace;
    }
  }

  @override
  bool shouldRepaint(_DottedLinePainter oldDelegate) {
    return oldDelegate.color != color ||
        oldDelegate.dashHeight != dashHeight ||
        oldDelegate.dashSpace != dashSpace ||
        oldDelegate.strokeWidth != strokeWidth;
  }
}

class OrderTimelineStep {
  final String title;
  final String time;
  final bool isCompleted;

  OrderTimelineStep({
    required this.title,
    required this.time,
    this.isCompleted = false,
  });
}
