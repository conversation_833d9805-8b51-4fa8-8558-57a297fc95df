import 'dart:async';

import 'package:flutter/material.dart';
import 'package:groceryboouser/Styles/app_colors.dart';

typedef VoidCallback = void Function();

class AnimatedShimmerButton extends StatefulWidget {
  final VoidCallback? onTimerComplete;
  final VoidCallback? onBackDisabled;
  final VoidCallback? onBackPressed;

  const AnimatedShimmerButton({
    super.key,
    this.onTimerComplete,
    this.onBackDisabled,
    this.onBackPressed,
  });

  @override
  State<AnimatedShimmerButton> createState() => _AnimatedShimmerButtonState();
}

class _AnimatedShimmerButtonState extends State<AnimatedShimmerButton> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  bool _isComplete = false;
  int _seconds = 6; // Start countdown from 6 seconds
  Timer? _timer;
  bool _canGoBack = true;
  double _progress = 0.0; // Progress for the loading effect (0.0 to 1.0)

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 6), // Animation matches timer duration
    );

    // Update the progress with animation
    _controller.addListener(() {
      setState(() {
        _progress = _controller.value;
      });
    });

    // Start the animation
    _controller.forward();

    // Start countdown timer
    _startCountdown();
  }

  void _startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          if (_seconds > 0) {
            _seconds--;

            // After 3 seconds, disable back button
            if (_seconds == 3) {
              _canGoBack = false;
              // Make sure to call the onBackDisabled callback
              if (widget.onBackDisabled != null) {
                widget.onBackDisabled!();
              }
            }
          } else {
            _isComplete = true;
            _controller.stop();
            timer.cancel();
            if (widget.onTimerComplete != null) {
              widget.onTimerComplete!();
            }
          }
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      // Prevent back button after 3 seconds
      canPop: _canGoBack,
      child: Container(
        height: 45,
        width: double.infinity,
        decoration: BoxDecoration(
          color: AppColors.blackColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Stack(
          children: [
            // Progress bar overlay that grows with the timer
            if (!_isComplete)
              Positioned.fill(
                child: IgnorePointer(
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CustomPaint(
                      painter: LoadingProgressPainter(_progress),
                    ),
                  ),
                ),
              ),

            // Text centered on top of everything with check icon when complete
            Center(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    _isComplete ? '' : 'Seems Great (0:0${_seconds > 0 ? _seconds : 0})',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                    ),
                  ),
                  if (_isComplete) ...[
                    const SizedBox(width: 8),
                    const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 18,
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class LoadingProgressPainter extends CustomPainter {
  final double progress;

  LoadingProgressPainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    // Calculate the width of the progress bar based on animation progress
    final progressWidth = size.width * progress;

    // Create the progress rect that grows from left to right
    final progressRect = Rect.fromLTWH(0, 0, progressWidth, size.height);

    // Create a gradient that shows a silver/metallic effect
    final gradient = LinearGradient(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
      colors: [
        Color(0xff464649), // Darker gray at left edge
        Color(0xff464649), // Lighter silver in middle
        Color(0xff464649), // Almost white at progressing edge
      ],
      stops: const [0.0, 0.7, 1.0],
    );

    final paint = Paint()
      ..shader = gradient.createShader(progressRect)
      ..blendMode = BlendMode.srcOver;

    // Draw the progress bar
    canvas.drawRect(progressRect, paint);

    // Add a subtle highlight at the edge of the progress
    if (progressWidth > 0 && progressWidth < size.width) {
      final edgeWidth = 2.0;
      final edgeRect = Rect.fromLTWH(progressWidth - edgeWidth, 0, edgeWidth * 2, size.height);

      final edgePaint = Paint()
        ..color = Color(0xff464649)
        ..blendMode = BlendMode.srcOver;

      canvas.drawRect(edgeRect, edgePaint);
    }
  }

  @override
  bool shouldRepaint(covariant LoadingProgressPainter oldDelegate) => oldDelegate.progress != progress;
}
