import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:groceryboouser/Screens/Restaurant/model/restaurant_order_details_model.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_icons.dart';

class TrackOrderView extends StatelessWidget {
  final StatusHistory steps;
  final String? time;
  final bool isLoading;
  final bool isLastStep;
  final bool isActive;

  const TrackOrderView({
    Key? key,
    required this.steps,
    this.isActive = false,
    this.time,
    this.isLoading = false,
    this.isLastStep = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Step indicator
                  Column(
                    children: [
                      _buildStepIndicator(isLastStep),
                      if (!isLastStep) _buildDottedLine(),
                    ],
                  ),
                  const SizedBox(width: 12),
                  // Step content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          steps.status ?? "",
                          style: AppTextStyle.mediumMedium.copyWith(
                            color: isActive ? AppColors.textColor : AppColors.textMuted,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          time ?? "",
                          style: AppTextStyle.smallRegular.copyWith(
                            color: isActive ? AppColors.textMuted : Colors.grey[500],
                          ),
                        ),
                        if (!isLastStep) const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          )
        ],
      ),
    );
  }

  Widget _buildDottedLine() {
    return Container(
      height: 60,
      width: 24,
      child: CustomPaint(
        painter: _DottedLinePainter(
          color: AppColors.steeperDottedLineColor,
          dashHeight: 3,
          dashSpace: 3,
          strokeWidth: 2,
        ),
      ),
    );
  }

  Widget _buildStepIndicator(
    bool isLastStep,
  ) {
    if (isLastStep) {
      // Completed step
      return Container(
        width: 24,
        height: 24,
        decoration: const BoxDecoration(
          color: Colors.white,
          shape: BoxShape.circle,
        ),
        child: SvgPicture.asset(
          'assets/images/right_click.svg',
          width: 24,
          height: 24,
        ),
      );
    }
    return Container(
      width: 24,
      height: 24,
      padding: const EdgeInsets.all(2),
      child: Image.asset(
        timerLoading,
        width: 24,
        height: 24,
      ),
    );
  }
}

class _DottedLinePainter extends CustomPainter {
  final Color color;
  final double dashHeight;
  final double dashSpace;
  final double strokeWidth;

  _DottedLinePainter({
    required this.color,
    required this.dashHeight,
    required this.dashSpace,
    required this.strokeWidth,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    final centerX = size.width / 2;
    double startY = 0;

    while (startY < size.height) {
      // Draw a small dash
      canvas.drawLine(
        Offset(centerX, startY),
        Offset(centerX, startY + dashHeight),
        paint,
      );

      // Move to the next dash position
      startY += dashHeight + dashSpace;
    }
  }

  @override
  bool shouldRepaint(_DottedLinePainter oldDelegate) {
    return oldDelegate.color != color ||
        oldDelegate.dashHeight != dashHeight ||
        oldDelegate.dashSpace != dashSpace ||
        oldDelegate.strokeWidth != strokeWidth;
  }
}

class OrderTimelineStep {
  final String title;
  final String time;
  final bool isCompleted;

  OrderTimelineStep({
    required this.title,
    required this.time,
    this.isCompleted = false,
  });
}
