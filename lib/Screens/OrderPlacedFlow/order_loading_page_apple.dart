import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:groceryboouser/Screens/OrderPlacedFlow/OrderHistory/animated_button_widget.dart';
import 'package:groceryboouser/Screens/OurServices/MyBasketScreen/controller/MyBasketController.dart';
import 'package:groceryboouser/Screens/OurServices/MyBasketScreen/model/MultiBasketModel.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_icons.dart';
import 'package:groceryboouser/Utilities/string_extentions.dart';
import 'package:groceryboouser/Utils/env_config.dart';
import 'package:groceryboouser/Utils/payment_helper.dart';
import 'package:groceryboouser/Utils/preference_utils.dart';
import 'package:groceryboouser/Widgets/app_asset_image.dart';
import 'package:video_player/video_player.dart';

class OrderLoadingPageApple extends StatefulWidget {
  final String? address;
  final String? deliveryTime;
  final String? storeName;
  final List<Map<String, String>>? items;
  // final CardDetails? cardDetails;
  // final String? pmId;
  final String? phoneNumber;
  // final String? paymentName;
  final String? storeId;
  final String? instruction;
  final String? isWallet;
  final String? isPinned;
  final String? walletAmount;
  final String? paystackAmount;
  final String? pinnedId;
  final String? repeatedOn;
  final String? day;
  final String? startDate;
  final String? endDate;
  final String? time;
  final String? cusDate;
  final String? count;
  final String? tranactionId;
  final String? status;
  final bool showToast;

  const OrderLoadingPageApple({
    Key? key,
    this.address,
    this.deliveryTime,
    this.storeName,
    this.items,
    // this.cardDetails,
    // this.pmId,
    this.phoneNumber,
    // this.paymentName = "stripe",
    this.storeId = "",
    this.instruction = "",
    this.isWallet = "no",
    this.isPinned = "no",
    this.walletAmount = "",
    this.paystackAmount = "",
    this.pinnedId = "",
    this.repeatedOn = "",
    this.day = "",
    this.startDate = "",
    this.endDate = "",
    this.time = "",
    this.cusDate = "",
    this.count = "",
    this.tranactionId = "",
    this.status = "",
    this.showToast = false,
  }) : super(key: key);

  @override
  State<OrderLoadingPageApple> createState() => _OrderLoadingPageAppleState();
}

class _OrderLoadingPageAppleState extends State<OrderLoadingPageApple> {
  VideoPlayerController? controller;
  String orderLoading = "assets/order_loading2.mp4";
  bool isInitialized = false;
  int _seconds = 6;
  MyBasketController myBasketController = Get.put(MyBasketController());
  final storeList = ValueNotifier<List<Store>>([]);
  final stripeKey = EnvConfig.stripePublishKey;
  String beginCheckoutKey = '';

  bool _showBackButton = true; // Control visibility of back button
  @override
  void initState() {
    super.initState();
    _initializeVideo();
    _startTimer();
    getStoreDetail();

    // Auto-call Stripe checkout API after 3 seconds
    Future.delayed(Duration(seconds: 3), () {
      if (mounted) {
        _callStripeCheckoutApi();
      }
    });
  }

  Future<void> _callStripeCheckoutApi() async {
    try {
      paymentMethodCreate();
    } catch (e) {
      log('Error processing payment: $e');
    }
  }

  _notify() {
    if (mounted) {
      setState(() {});
    }
  }

  void _startTimer() {
    Future.delayed(Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          if (_seconds > 0) {
            _seconds--;
            _startTimer();
          }
        });
      }
    });
  }

  void getStoreDetail() async {
    // Wait for the basket data to be loaded if needed
    if (myBasketController.multiBasketData.value.stores == null) {
      await myBasketController.displayMultiBasketListNew();
      log('Basket data loaded:::::::::: ${myBasketController.showToast.value}');
    }

    List<Store> tempStoreList = [];

    // Safely access the stores list
    final stores = myBasketController.multiBasketData.value.stores;
    if (stores != null) {
      for (int i = 0; i < stores.length; i++) {
        final store = stores[i];
        if (store.products != null) {
          // Create a filtered list of products with value > 0
          List<Products> filteredProducts = [];
          for (var product in store.products!) {
            if ((product.productValue ?? 0) > 0) {
              filteredProducts.add(product);
            }
          }

          // Only add the store if it has products with value > 0
          if (filteredProducts.isNotEmpty) {
            // Create a copy of the store with only the filtered products
            Store filteredStore = Store(
              store_name: store.store_name,
              products: filteredProducts,
            );

            tempStoreList.add(filteredStore);
          }
        }
      }
    }

    // Update the store list
    storeList.value = tempStoreList;
  }

  Future<void> _initializeVideo() async {
    try {
      log('Starting video initialization...');

      controller = VideoPlayerController.asset(orderLoading);
      log('Controller created');

      await controller!.initialize().then((_) {
        log('Video initialized successfully');
        if (mounted) {
          setState(() {
            isInitialized = true;
          });
        }

        controller!.addListener(() {
          if (mounted) {
            setState(() {});
          }
        });

        controller!.setLooping(true);
        controller!.play();
        log('Video started playing');
      });
    } catch (e) {
      log('Video Error: $e');
      if (mounted) {
        setState(() {
          isInitialized = false;
        });
      }
    }
  }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white, // Red background color
      body: Stack(
        alignment: Alignment.topCenter,
        children: [
          controller != null && isInitialized
              ? SizedBox(
                  height: MediaQuery.of(context).size.height * 0.5,
                  child: AspectRatio(
                    aspectRatio: controller!.value.aspectRatio,
                    child: VideoPlayer(controller!),
                  ),
                )
              : Center(
                  child: CircularProgressIndicator(
                    color: Colors.white,
                  ),
                ),
          // Bottom white card with order details
          Padding(
            padding: EdgeInsets.only(top: MediaQuery.of(context).size.height * 0.40),
            child: // Bottom white card with order details
                Container(
              width: double.infinity,
              margin: EdgeInsets.symmetric(horizontal: 8),
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(18),
                  topRight: Radius.circular(18),
                ),
                border: Border.all(
                  color: Colors.grey.withAlpha(50),
                  width: 1,
                  style: BorderStyle.solid,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Fixed content at the top
                  Padding(
                    padding: EdgeInsets.only(left: 22, right: 22, top: 20),
                    child: Text(
                      'Placing your order',
                      style: AppTextStyle.bodyLarge,
                    ),
                  ),

                  // Scrollable content area
                  Expanded(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.symmetric(horizontal: 22, vertical: 10),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (PreferenceManagerUtils.getYourAddress().isNotEmpty) ...[
                            // Address with location icon
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                AppAssetImage(loactionNewIcon, width: 18, height: 18),
                                SizedBox(width: 10),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        '${PreferenceManagerUtils.getYourAddress()}'.split(',').take(2).join(','),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        style: AppTextStyle.smallMedium.copyWith(
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      Row(
                                        children: [
                                          Text(
                                            'Delivering to',
                                            style: AppTextStyle.smallRegular.copyWith(color: AppColors.textMuted),
                                          ),
                                          Text(
                                            ' ${myBasketController.addressModel!.savedName}'.capitalizeFirstofEach,
                                            style: AppTextStyle.smallRegular.copyWith(color: AppColors.textMuted),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                          SizedBox(height: 8),
                          Divider(
                            color: AppColors.dividerColor,
                            thickness: 1,
                          ),
                          SizedBox(height: 8),

                          // Delivery time
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              AppAssetImage(fastDeliveryNewIcon, width: 18, height: 18),
                              SizedBox(width: 10),
                              Text(
                                widget.deliveryTime ?? 'Fast delivery: 20-35+ mins',
                                style: AppTextStyle.smallMedium,
                              ),
                            ],
                          ),
                          SizedBox(height: 8),
                          Divider(
                            color: AppColors.dividerColor,
                            thickness: 1,
                          ),
                          SizedBox(height: 8),

                          // Store list with items
                          ValueListenableBuilder<List<Store>>(
                            valueListenable: storeList,
                            builder: (context, stores, _) {
                              return Column(
                                children: stores.map((store) {
                                  return Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      // Store header
                                      Row(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          AppAssetImage(superMarketNewIcon, width: 18, height: 18),
                                          SizedBox(width: 10),
                                          Expanded(
                                            child: Text(
                                              store.store_name ?? 'Store',
                                              style: AppTextStyle.smallMedium,
                                            ),
                                          ),
                                        ],
                                      ),
                                      SizedBox(height: 4),

                                      // Store items
                                      if (store.products != null && store.products!.isNotEmpty)
                                        Padding(
                                          padding: EdgeInsets.only(left: 28),
                                          child: Column(
                                            crossAxisAlignment: CrossAxisAlignment.start,
                                            children: store.products!.map((product) {
                                              return Padding(
                                                padding: const EdgeInsets.only(bottom: 4),
                                                child: Row(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      '${product.productValue ?? 0}×',
                                                      style: AppTextStyle.smallRegular.copyWith(
                                                        color: AppColors.blackColor,
                                                        fontWeight: FontWeight.w600,
                                                      ),
                                                    ),
                                                    Expanded(
                                                      child: Text(
                                                        ' ${product.productDetails?.name ?? 'Product'}',
                                                        maxLines: 2,
                                                        style: AppTextStyle.smallRegular
                                                            .copyWith(color: AppColors.textMuted),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              );
                                            }).toList(),
                                          ),
                                        ),
                                      SizedBox(height: 8),
                                    ],
                                  );
                                }).toList(),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Fixed buttons at the bottom
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 22, vertical: 16),
                    child: Column(
                      children: [
                        Container(
                          width: double.infinity,
                          child: AnimatedShimmerButton(
                            onTimerComplete: () {
                              // Handle timer complete
                              setState(() {
                                // Timer completed
                              });
                            },
                            onBackDisabled: () {
                              // Back button disabled after 3 seconds
                              setState(() {
                                _showBackButton = false; // This will hide the Go Back button
                              });
                            },
                          ),
                        ),
                        SizedBox(height: 8),
                        Container(
                          width: double.infinity,
                          child: TextButton(
                            onPressed: (_showBackButton)
                                ? () {
                                    // Handle go back
                                    Navigator.pop(context);
                                  }
                                : null,
                            child: Text(
                              _showBackButton ? 'Go Back' : 'Processing...',
                              style: TextStyle(
                                color: Colors.black,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future paymentMethodCreate() async {
    log(' 1::::::::Creating payment method with Apple Pay...');
    // if (widget.showToast) {
    //   Utility.toast(message: ' 1::::::::Creating payment method with Apple Pay...');
    // }

    try {
      // Create a payment method using the card details
      final paymentIntent = await PaymentHelper.applePay(
        showToast: widget.showToast,
        params: StripPaymentParams(
          context: context,
          amount: widget.paystackAmount ?? '10',
          currency: 'USD',
        ),
      );
      // if (widget.showToast) {
      //   Utility.toast(message: '2::::::::paymentIntent is null');
      // }
      if (paymentIntent != null) {
        log(' 2::::::::Creating payment method with Apple Pay...');
        // if (widget.showToast) {
        //   Utility.toast(message: '3::::::::paymentIntent is not null');
        // }
        String? strPinId;
        strPinId = myBasketController.pinId.value;

        // if (widget.status == false) {
        //   // widget.isPinned = "no";

        //   myBasketController.orderPlaceAPI(
        //       context,
        //       widget.phoneNumber,
        //       "stripe",
        //       widget.storeId.toString(),
        //       widget.instruction,
        //       widget.isWallet,
        //       'no',
        //       widget.walletAmount,
        //       widget.paystackAmount,
        //       strPinId,
        //       widget.repeatedOn,
        //       widget.day,
        //       widget.startDate,
        //       widget.endDate,
        //       widget.time,
        //       widget.cusDate,
        //       widget.count,
        //       paymentIntent.id.toString(),
        //       'success');

        //   if (widget.showToast) {
        //     Utility.toast(message: '4:::::::: after orderPlaceAPI with widget.status == false');
        //   }
        // } else if (widget.status == true) {
        //   // widget.isPinned = "yes";

        //   myBasketController.orderPlaceAPI(
        //       context,
        //       widget.phoneNumber,
        //       "",
        //       widget.storeId.toString(),
        //       widget.instruction,
        //       widget.isWallet,
        //       'yes',
        //       widget.walletAmount,
        //       widget.paystackAmount,
        //       strPinId,
        //       widget.repeatedOn,
        //       widget.day,
        //       widget.startDate,
        //       widget.endDate,
        //       widget.time,
        //       widget.cusDate,
        //       widget.count,
        //       paymentIntent.id.toString(),
        //       'success');

        //   if (widget.showToast) {
        //     Utility.toast(message: '5:::::::: after orderPlaceAPI with widget.status == true');
        //   }
        // }

        // if (widget.showToast) {
        //   Utility.toast(message: '5:::::::: after orderPlaceAPI with widget.status == true');
        // }
        myBasketController.orderPlaceAPI(
            context,
            widget.phoneNumber,
            "stripe",
            widget.storeId.toString(),
            widget.instruction,
            widget.isWallet,
            'no',
            widget.walletAmount,
            widget.paystackAmount,
            strPinId,
            widget.repeatedOn,
            widget.day,
            widget.startDate,
            widget.endDate,
            widget.time,
            widget.cusDate,
            widget.count,
            paymentIntent.id.toString(),
            'success');
      } else {
        log(' 3::::::::Creating payment method with Apple Pay...');
        Navigator.pop(context);
        // Payment failed
        log('Payment method creation failed');
        // if (widget.showToast) {
        //   Utility.toast(message: '6:::::::: Payment method creation failed');
        // }
        // isLoading.value = false;
      }
    } catch (e) {
      log(' 4::::::::Creating payment method with Apple Pay...');
      log('Error creating payment method: $e');
      // if (widget.showToast) {
      //   Utility.toast(message: '7:::::::: Error creating payment method: $e');
      // }
      throw e;
    }
  }
}

// Future<void> doStripeCheckOutAPi({CardDetails? cardDetails, String? pmId}) async {
//   try {
//     myBasketController.isButtonLoading.value = true;
//     log(stripeKey.toString() + 'stripe key ++++++++++++++++++++++++++++++');
//     Stripe.publishableKey = stripeKey;
//     Stripe.merchantIdentifier = 'grocery.flutter.stripe.test';
//     Stripe.urlScheme = 'flutterstripe';

//     await Stripe.instance.applySettings().onError((error, stackTrace) {
//       return log('------ apply setting crash');
//     });

//     await beginCheckoutApiCall();

//     PaymentMethod? paymentMethod;

//     if (cardDetails != null) {
//       await Stripe.instance.dangerouslyUpdateCardDetails(cardDetails).onError((error, stackTrace) {
//         return log('------ apply dangerouslyUpdateCardDetails crash');
//       });

//       await Stripe.instance
//           .createPaymentMethod(
//         params: const PaymentMethodParams.card(
//           paymentMethodData: PaymentMethodData(),
//         ),
//       )
//           .then((value) {
//         paymentMethod = value;
//       }).catchError((e) {
//         myBasketController.isButtonLoading.value = false;
//         Navigator.pop(context);

//         Utility.toast(message: e.error.localizedMessage.toString());
//       });
//       log('${paymentMethod?.id} ----');
//     }

//     if (beginCheckoutKey.isNotEmpty && (paymentMethod?.id ?? pmId) != null) {
//       // Navigator.pop(context);

//       await myBasketController.orderPlaceAPI(
//         context,
//         widget.phoneNumber,
//         widget.paymentName,
//         widget.storeId,
//         widget.instruction,
//         widget.isWallet,
//         widget.isPinned,
//         widget.walletAmount,
//         widget.paystackAmount,
//         widget.pinnedId,
//         widget.repeatedOn,
//         widget.day,
//         widget.startDate,
//         widget.endDate,
//         widget.time,
//         widget.cusDate,
//         widget.count,
//         widget.tranactionId,
//         widget.status,
//         paymentMethod?.id ?? pmId,
//         beginCheckoutKey,
//       );
//     }

//     myBasketController.isButtonLoading.value = false;
//   } catch (e, s) {
//     log('${cardDetails}card details inside catch final');

//     myBasketController.isButtonLoading.value = false;
//     log('ERROR________--------');
//     log(e.toString());

//     s.toString();
//   }
// }

// Future<void> beginCheckoutApiCall() async {
//   if (await ApiManager.checkInternet()) {
//     var request = <String, dynamic>{};
//     request['user_id'] = PreferenceManagerUtils.getUserId().toString();

//     BeingCheckoutResponse response = BeingCheckoutResponse.fromJson(
//       await ApiManager().postCall(beginCheckoutApi, request, context),
//     );

//     if (response.statusCode == 200 && response.success == true && response.data != null) {
//       beginCheckoutKey = response.data ?? '';
//       _notify();
//       log("${beginCheckoutKey}begin checkout key");
//     }
//   } else {
//     if (mounted) {
//       Utility.toast(message: 'No Internet Connection');
//     }
//   }
// }
