import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:groceryboouser/Party_Phase/utility/utility.dart';
import 'package:groceryboouser/Screens/MarketTabP3/featured_basket/widget/qty_button_widget.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';

import '../../../../Styles/my_icons.dart';

class PromoProductView extends StatefulWidget {
  const PromoProductView({
    super.key,
    this.onRemove,
    this.onAdd,
    this.qty,
    this.isStoreClose = false,
    this.image,
    this.name,
    this.price,
  });
  final void Function()? onRemove;
  final void Function()? onAdd;
  final int? qty;
  final bool isStoreClose;
  final String? image;
  final String? name;
  final String? price;
  @override
  State<PromoProductView> createState() => _PromoProductViewState();
}

class _PromoProductViewState extends State<PromoProductView> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10),
      child: Row(
        children: [
          SizedBox(
            height: 66.h,
            width: 69.w,
            child: UtilityForParty.imageLoader(
              url: widget.image ?? '',
              placeholder: grey_bg,
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          SizedBox(
            width: 16,
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.name ?? '',
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: AppTextStyle.smallMedium,
                ),
                // SizedBox(
                //   height: 4,
                // ),
                // Text(
                //   '\$${double.tryParse(widget.price ?? '')?.toStringAsFixed(2)}',
                //   style: AppTextStyle.smallMedium.copyWith(color: grey_500),
                // ),
                // QtyButtonWidget(),
              ],
            ),
          ),
          QtyButtonWidget(
            isClosedStore: widget.isStoreClose,
            qty: widget.qty,
            onAdd: widget.onAdd,
            onRemove: widget.onRemove,
          ),
        ],
      ),
    );
  }
}
