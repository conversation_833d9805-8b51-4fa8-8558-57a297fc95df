import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:groceryboouser/Party_Phase/Create_Party/widget/app_textform_field_Widget.dart';
import 'package:groceryboouser/Party_Phase/utility/utility.dart';
import 'package:groceryboouser/Screens/Location/controller/api_manager.dart';
import 'package:groceryboouser/Screens/Networks/api_endpoint.dart';
import 'package:groceryboouser/Screens/PaymentFlow/withdraw/model/comman_response.dart';
import 'package:groceryboouser/Styles/app_colors.dart';
import 'package:groceryboouser/Styles/app_text_style.dart';
import 'package:groceryboouser/Styles/my_colors.dart';
import 'package:groceryboouser/Styles/my_icons.dart';
import 'package:groceryboouser/Utilities/Constant.dart';
import 'package:groceryboouser/Utils/text_utilities.dart';
import 'package:groceryboouser/Widgets/common_button_widget.dart';

class SupportPage extends StatefulWidget {
  const SupportPage({super.key});

  @override
  State<SupportPage> createState() => _SupportPageState();
}

class _SupportPageState extends State<SupportPage> {
  final activeFocusColor = ValueNotifier<String>('');
  final isButtonLoading = ValueNotifier<bool>(false);
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final mobileController = TextEditingController();
  final messageController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    nameController.dispose();
    emailController.dispose();
    mobileController.dispose();
    messageController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }

  Future<void> createSupportAPI() async {
    if (formKey.currentState!.validate()) {}
  }

  Future<void> supportAPI() async {
    isButtonLoading.value = true;
    if (await ApiManager.checkInternet()) {
      var request = <String, dynamic>{};

      request['name'] = nameController.text.trim();
      request['email'] = emailController.text.trim();
      request['phone_number'] = mobileController.text.trim();
      request['message'] = messageController.text.trim();

      request['subject'] = 'Support';
      request['user_id'] = '1';

      CommonResponse response = CommonResponse.fromJson(
        await ApiManager().postCall(createSupport, request, context),
      );
      if (response.success == true) {
        isButtonLoading.value = false;
      }
      isButtonLoading.value = false;
      Utility.toast(message: response.message);
      Navigator.pop(context);
    } else {
      isButtonLoading.value = false;
      Utility.toast(message: 'No Internet Connection');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: AppColors.white,
        leading: InkWell(
          onTap: () {
            Get.back();
          },
          child: Padding(
            padding: EdgeInsets.only(left: 22.w),
            child: Row(
              children: [
                SvgPicture.asset(
                  icon_new_back_Arrow,
                ),
              ],
            ),
          ),
        ),
        centerTitle: true,
        title: Text(
          'Support',
          style: AppTextStyle.largeMedium,
          textAlign: TextAlign.center,
        ),
      ),
      body: ValueListenableBuilder<String>(
          valueListenable: activeFocusColor,
          builder: (context, value, _) {
            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Form(
                  key: formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      AppTextformFieldWidget(
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your name';
                          }
                          return null;
                        },
                        onTap: () {
                          activeFocusColor.value = 'name';
                        },
                        onOutSideTap: (value) {
                          activeFocusColor.value = '';
                          FocusScope.of(context).unfocus();
                        },
                        style: TextStyle(
                          color: value == 'name' ? black : grey_600,
                        ),
                        focusBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: pink_d03784, width: 3.0),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        textInputAction: TextInputAction.next,
                        keyboardType: TextInputType.name,
                        inputFormatters: [
                          FilteringTextInputFormatter.deny(RegExp(r'[^a-zA-Z ]')),
                        ],
                        controller: nameController,
                        fillColor: value == 'name' ? white : grey_e8e8e8,
                        hintText: 'Name',
                        hintStyle: TextStyle(color: grey_600, fontFamily: switzer_reuglar),
                      ),
                      SizedBox(
                        height: 16,
                      ),
                      AppTextformFieldWidget(
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your email';
                          } else if (!UtilityForParty.isValidEmail(value)) {
                            return 'Please enter a valid email address';
                          }
                          return null;
                        },
                        onTap: () {
                          activeFocusColor.value = 'email';
                        },
                        onOutSideTap: (value) {
                          activeFocusColor.value = '';
                          FocusScope.of(context).unfocus();
                        },
                        style: TextStyle(
                          color: value == 'email' ? black : grey_600,
                        ),
                        focusBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: pink_d03784, width: 3.0),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        textInputAction: TextInputAction.next,
                        keyboardType: TextInputType.emailAddress,
                        controller: emailController,
                        fillColor: value == 'email' ? white : grey_e8e8e8,
                        hintText: 'Email',
                        hintStyle: TextStyle(color: grey_600, fontFamily: switzer_reuglar),
                      ),
                      SizedBox(
                        height: 16,
                      ),
                      AppTextformFieldWidget(
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your mobile number';
                          }
                          return null;
                        },
                        onTap: () {
                          activeFocusColor.value = 'mobile';
                        },
                        onOutSideTap: (value) {
                          activeFocusColor.value = '';
                          FocusScope.of(context).unfocus();
                        },
                        style: TextStyle(
                          color: value == 'mobile' ? black : grey_600,
                        ),
                        focusBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: pink_d03784, width: 3.0),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        textInputAction: TextInputAction.next,
                        keyboardType: TextInputType.phone,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                        ],
                        controller: mobileController,
                        fillColor: value == 'mobile' ? white : grey_e8e8e8,
                        hintText: 'Mobile',
                        hintStyle: TextStyle(color: grey_600, fontFamily: switzer_reuglar),
                      ),
                      SizedBox(
                        height: 16,
                      ),
                      AppTextformFieldWidget(
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your message';
                          }
                          return null;
                        },
                        onTap: () {
                          activeFocusColor.value = 'message';
                        },
                        onOutSideTap: (value) {
                          activeFocusColor.value = '';
                          FocusScope.of(context).unfocus();
                        },
                        style: TextStyle(
                          color: value == 'message' ? black : grey_600,
                        ),
                        focusBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: pink_d03784, width: 3.0),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        textInputAction: TextInputAction.done,
                        controller: messageController,
                        fillColor: value == 'message' ? white : grey_e8e8e8,
                        hintText: 'Message',
                        hintStyle: TextStyle(color: grey_600, fontFamily: switzer_reuglar),
                        maxLines: 5,
                      ),
                      SizedBox(
                        height: 30,
                      ),
                      ValueListenableBuilder(
                          valueListenable: isButtonLoading,
                          builder: (context, loading, _) {
                            return CommonButtonWidget(
                              isLoading: loading,
                              title: 'Submit',
                              onTap: () {
                                if (formKey.currentState!.validate()) {
                                  supportAPI();
                                }
                              },
                            );
                          }),
                    ],
                  ),
                ),
              ),
            );
          }),
    );
  }
}
