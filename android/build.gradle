buildscript {
    ext.kotlin_version = '2.1.20'
    repositories {
        google()
        jcenter()
        //mavenCentral()
        // maven {
        //     url 'https://dl.google.com/dl/android/maven2'
        // }
        // maven { url "https://jitpack.io" }
        // maven { url "https://maven.arthenica.com/repository/ffmpeg-kit/com/arthenica/" }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.3.15'

    }
}

allprojects {
    repositories {
        google()
        jcenter()
        mavenCentral()
    }
    //** add this line
    configurations.all {
        resolutionStrategy {
            force "com.google.android.gms:play-services-location:21.0.1"
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

task clean(type: Delete) {
    delete rootProject.buildDir


}
