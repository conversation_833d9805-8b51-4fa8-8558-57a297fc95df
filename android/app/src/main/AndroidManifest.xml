<manifest xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:tools="http://schemas.android.com/tools"
    package="com.groceryboo.user">

    <uses-permission android:name="android.permission.INTERNET"/>

<!--    <uses-permission android:name="android.permission.READ_PHONE_STATE"/>-->
<!--    <uses-permission android:name="android.permission.READ_CONTACTS" />-->
<!--    <uses-permission android:name="android.permission.WRITE_CONTACTS" />-->
    <uses-permission android:name="android.permission.GET_TASKS" />
<!--    <uses-permission android:name="android.permission.READ_CALL_LOG" />-->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <!-- <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION"/> -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <!-- Remove bcz of error in play store  -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" tools:node="remove"/> 
    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" tools:node="remove" /> 

    
<!--    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"/>-->
<!--    <uses-permission android:name="android.permission.READ_PHONE_STATE" />-->


    <!--    <queries>-->
<!--        <package android:name="com.facebook.katana" />-->
<!--    </queries>-->

    <queries>
        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" /> <!-- allows app to access Facebook app features -->
        <provider android:authorities="com.facebook.orca.provider.PlatformProvider" /> <!-- allows sharing to Messenger app -->
    </queries>

     <!-- Provide required visibility configuration for API level 30 and above -->
  <queries>
    <intent>
      <action android:name="android.intent.action.VIEW" />
      <data android:scheme="https" />
    </intent>
    <intent>
      <action android:name="android.intent.action.DIAL" />
      <data android:scheme="tel" />
    </intent>
    <intent>
      <action android:name="android.intent.action.SENDTO" />
      <data android:scheme="smsto" />
    </intent>
  </queries>

    <application
        android:label="LOVGRUB"
        android:name="${applicationName}"
        android:icon="@mipmap/ic_launcher"
        android:requestLegacyExternalStorage="true"
        android:largeHeap="true"
        android:usesCleartextTraffic="true"
        android:allowBackup="false"
        android:fullBackupOnly="false"
        >
        <uses-library android:name="org.apache.http.legacy" android:required="false"/>
        <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id"/>
        <meta-data android:name="com.facebook.sdk.ClientToken" android:value="@string/facebook_client_token"/>
        <activity
            android:name="com.facebook.FacebookActivity"
            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
            android:label="@string/app_name" />

        <activity
            android:name="com.facebook.CustomTabActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="@string/fb_login_protocol_scheme" />
            </intent-filter>
        </activity>

        <activity
            android:name=".MainActivity"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:exported="true"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">

            <!-- add this intent filter for deeplinking -->
            <!-- <intent-filter android:autoVerify="true"> -->
                <!-- <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="grocerybooapp.page.link"
                    android:scheme="https" /> -->
            <!-- </intent-filter> -->


            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <!-- Displays an Android View that continues showing the launch screen
                 Drawable until Flutter paints its first frame, then this splash
                 screen fades out. A splash screen is useful to avoid any visual
                 gap between the end of Android's launch screen and the painting of
                 Flutter's first frame. -->
            <meta-data
              android:name="io.flutter.embedding.android.SplashScreenDrawable"
              android:resource="@drawable/launch_background"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
            
            <intent-filter>
                <action android:name="FLUTTER_NOTIFICATION_CLICK" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter> 
        </activity>


        <activity
            android:name="com.aboutyou.dart_packages.sign_in_with_apple.SignInWithAppleCallback"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="signinwithapple" />
                <data android:path="callback" />
            </intent-filter>
        </activity>




        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="Grocery Boo" />

        <meta-data android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyAlEQiJi8a8Lw9ekhDfP7ssAO_uFpZvlJ8"/>
<!--            android:value="AIzaSyAa5gcSQAa52aJy9j9-BE2IzYtoD7VhlDc"/>-->
            <!-- android:value="AIzaSyAZ1cz2GDW11Y8LO325bM7vEBSOw9TjesM"/> -->
            <!-- android:value="AIzaSyAYXlmPYw-EDklqtaiCkmAWrQpIDNu5mJI"/> -->
            <!-- android:value="AIzaSyDpzBK_Tn6DGCz-K2eLEx0hdc_GI-ZwNlE"/> -->
            
            <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.com.shekarmudaliyar.social_share"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths" />

            </provider>

        <meta-data
        android:name="com.google.android.gms.wallet.api.enabled"
        android:value="true" />

    </application>
</manifest>
